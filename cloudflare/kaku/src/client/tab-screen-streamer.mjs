// Tab Screen Streamer - Browser Injectable Script
// Captures and streams the current tab's screen content to the UI layout component
(function () {
  const config = {
    frameRate: 15,
    debug: true,
  };

  let socket = null;
  let pc = null;
  let screenStream = null;
  let isInitialized = false;
  let isStreaming = false;
  let inputChannel = null;

  // Frame dimensions
  let FRAME_WIDTH = 1366;
  let FRAME_HEIGHT = 768;

  function log(...args) {
    if (config.debug) {
      console.log('[kazeel][tab-screen-streamer]', ...args);
    }
  }

  function error(...args) {
    console.error('[kazeel][tab-screen-streamer]', ...args);
  }

  /**
   * Initialize tab screen streamer with WebSocket and WebRTC connections
   * This method sets up the connections but doesn't start streaming
   */
  async function init(wsEndpoint, viewPort) {
    if (isInitialized) {
      log('Tab screen streamer already initialized');
      return 'ALREADY_INITIALIZED';
    }

    try {
      log('🔧 [init] Starting tab screen streamer initialization...');
      log('🔧 [init] WebSocket endpoint:', wsEndpoint);
      log('🔧 [init] Viewport:', viewPort);

      // Update frame dimensions
      FRAME_WIDTH = viewPort.width;
      FRAME_HEIGHT = viewPort.height;

      // Step 1: Setup WebSocket connection
      log('🔧 [init] Step 1: Setting up WebSocket connection...');
      await connectToSocket(wsEndpoint);
      log('✅ [init] WebSocket connection established');

      // Step 2: Setup WebRTC connection
      log('🔧 [init] Step 2: Setting up WebRTC connection...');
      await connectToWebRTC();
      log('✅ [init] WebRTC connection established');

      // Step 3: Setup message handlers
      setupSocketMessageHandlers();
      log('✅ [init] Message handlers set up');

      isInitialized = true;
      log('✅ [init] Tab screen streamer initialization completed');
      return 'SUCCESS';
    } catch (err) {
      error('❌ [init] Initialization failed:', err);
      throw err;
    }
  }

  /**
   * Connect to WebSocket and wait for it to be ready
   */
  async function connectToSocket(wsEndpoint) {
    return new Promise((resolve, reject) => {
      try {
        log('🔧 [connectToSocket] Connecting to WebSocket:', wsEndpoint);

        if (!wsEndpoint) {
          throw new Error(`[kazeel][tab-screen-streamer] WebSocket endpoint is required`);
        }

        socket = new WebSocket(wsEndpoint);

        // Expose socket to window for external access
        window.tabStreamerSocket = socket;
        log('✅ [connectToSocket] Socket exposed to window');

        socket.addEventListener('open', () => {
          log('✅ [connectToSocket] WebSocket connected successfully');
          resolve();
        });

        socket.addEventListener('error', (e) => {
          reject(
            new Error(
              `[kazeel][tab-screen-streamer] WebSocket connection failed: ${e.message || 'Unknown error'}`,
            ),
          );
        });

        socket.addEventListener('close', (e) => {
          log('🔌 [connectToSocket] WebSocket connection closed:', e.code, e.reason);
        });
      } catch (err) {
        error('❌ [connectToSocket] Error setting up WebSocket:', err);
        reject(err);
      }
    });
  }

  /**
   * Connect to WebRTC and set up peer connection
   */
  async function connectToWebRTC() {
    return new Promise((resolve, reject) => {
      try {
        log('🔧 [connectToWebRTC] Setting up WebRTC peer connection...');

        pc = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.cloudflare.com:3478' },
            {
              urls: 'turn:relay1.expressturn.com:3478',
              username: 'ef89RMU4SHUQMSOUU9',
              credential: 'jvkMMnQxWX4Qrhe3',
            },
          ],
        });
        log('✅ [connectToWebRTC] RTCPeerConnection created');

        pc.onicecandidate = (event) => {
          if (event.candidate && socket?.readyState === WebSocket.OPEN) {
            log('🔧 [connectToWebRTC] Sending ICE candidate');
            socket.send(
              JSON.stringify({
                type: 'candidate',
                candidate: event.candidate,
                source: 'tab-stream',
              }),
            );
          }
        };

        pc.onconnectionstatechange = () => {
          log('🔧 [connectToWebRTC] Connection state changed:', pc.connectionState);
        };

        pc.oniceconnectionstatechange = () => {
          log('🔧 [connectToWebRTC] ICE connection state changed:', pc.iceConnectionState);
        };

        log('✅ [connectToWebRTC] WebRTC peer connection setup completed');
        resolve();
      } catch (err) {
        error('❌ [connectToWebRTC] Error setting up WebRTC:', err);
        reject(err);
      }
    });
  }

  /**
   * Set up WebSocket message handlers
   */
  function setupSocketMessageHandlers() {
    socket.addEventListener('message', async (event) => {
      let msg;
      try {
        msg = JSON.parse(event.data);
      } catch (err) {
        log('❌ [setupSocketMessageHandlers] Failed to parse WebSocket message:', err);
        return;
      }

      log('📨 [setupSocketMessageHandlers] Received message:', msg.type);

      switch (msg.type) {
        case 'offer':
          // Only handle offers from UI
          if (msg.source === 'ui') {
            await pc.setRemoteDescription(new RTCSessionDescription(msg.offer));
            const answer = await pc.createAnswer();
            await pc.setLocalDescription(answer);
            socket.send(
              JSON.stringify({
                type: 'answer',
                answer,
                source: 'tab-stream',
              }),
            );
          }
          break;
        case 'answer':
          // Only handle answers from UI
          if (msg.source === 'ui') {
            await pc.setRemoteDescription(new RTCSessionDescription(msg.answer));
          }
          break;
        case 'candidate':
          // Only handle candidates from UI
          if (msg.source === 'ui') {
            await pc.addIceCandidate(new RTCIceCandidate(msg.candidate));
          }
          break;
        case 'ready':
          // UI is ready, create offer from tab stream side
          if (msg.source === 'ui') {
            log('🔧 [ready] UI ready, creating input channel and offer...');
            createInputChannel();

            const offer = await pc.createOffer();
            await pc.setLocalDescription(offer);
            socket.send(
              JSON.stringify({
                type: 'offer',
                offer,
                source: 'tab-stream',
              }),
            );
            log('✅ [ready] Offer sent with input channel');
          }
          break;
        case 'start-streaming':
          if (msg.viewport) {
            await startStreaming(msg.viewport);
          }
          break;
        case 'stop-streaming':
          await stopStreaming();
          break;
        default:
          log('❓ [setupSocketMessageHandlers] Unknown message type:', msg.type);
      }
    });
  }

  /**
   * Create input data channel for communication
   */
  function createInputChannel() {
    try {
      log('🔧 [createInputChannel] Creating input data channel...');

      inputChannel = pc.createDataChannel('input', {
        ordered: true,
      });

      inputChannel.onopen = () => {
        log('✅ [createInputChannel] Input channel opened');
      };

      inputChannel.onclose = () => {
        log('🔌 [createInputChannel] Input channel closed');
      };

      inputChannel.onerror = (error) => {
        error('❌ [createInputChannel] Input channel error:', error);
      };

      inputChannel.onmessage = async (event) => {
        try {
          log('📨 [createInputChannel] Received message:', event.data);
          const data = JSON.parse(event.data);

          // Handle input events or other messages from the UI
          if (data.type === 'request-frame') {
            // Request a new frame if needed
            log('📸 [createInputChannel] Frame request received');
          }
        } catch (err) {
          error('Failed to handle input channel message:', err);
        }
      };

      log('✅ [createInputChannel] Input channel created successfully');
    } catch (err) {
      error('❌ [createInputChannel] Failed to create input channel:', err);
      throw err;
    }
  }

  /**
   * Start streaming the current tab's screen content
   */
  async function startStreaming(viewPort) {
    if (isStreaming) {
      log('Tab screen streaming already active');
      return;
    }

    try {
      log('🎬 [startStreaming] Starting tab screen streaming...');

      // Update frame dimensions
      FRAME_WIDTH = viewPort.width;
      FRAME_HEIGHT = viewPort.height;

      log(`Updated frame dimensions to ${FRAME_WIDTH}x${FRAME_HEIGHT}`);

      // Get display media stream for current tab
      log('Requesting display media stream for current tab...');
      screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: config.frameRate,
          width: viewPort.width,
          height: viewPort.height,
        },
        preferCurrentTab: true,
      });
      log('Display media stream obtained successfully');

      // Add the video track to the peer connection
      const videoTrack = screenStream.getVideoTracks()[0];
      pc.addTrack(videoTrack, screenStream);

      isStreaming = true;
      log('✅ [startStreaming] Tab screen streaming started successfully');

      // Notify the UI that streaming has started
      if (socket?.readyState === WebSocket.OPEN) {
        socket.send(
          JSON.stringify({
            type: 'streaming-status',
            status: 'started',
            viewport: viewPort,
          }),
        );
      }
    } catch (err) {
      error('❌ [startStreaming] Failed to start tab screen streaming:', err);
      throw err;
    }
  }

  /**
   * Stop streaming
   */
  async function stopStreaming() {
    if (!isStreaming) {
      log('Tab screen streaming not active');
      return;
    }

    try {
      log('🛑 [stopStreaming] Stopping tab screen streaming...');

      if (screenStream) {
        screenStream.getTracks().forEach((track) => {
          track.stop();
          log('🔌 [stopStreaming] Stopped track:', track.kind);
        });
        screenStream = null;
      }

      isStreaming = false;
      log('✅ [stopStreaming] Tab screen streaming stopped successfully');

      // Notify the UI that streaming has stopped
      if (socket?.readyState === WebSocket.OPEN) {
        socket.send(
          JSON.stringify({
            type: 'streaming-status',
            status: 'stopped',
          }),
        );
      }
    } catch (err) {
      error('❌ [stopStreaming] Failed to stop tab screen streaming:', err);
      throw err;
    }
  }

  /**
   * Send status update to the UI layout
   */
  function sendStatusUpdate(status, data = {}) {
    if (socket?.readyState === WebSocket.OPEN) {
      const message = {
        type: 'tab-streaming-status',
        status,
        timestamp: Date.now(),
        source: 'tab-stream',
        ...data,
      };
      socket.send(JSON.stringify(message));
      log('📤 [sendStatusUpdate] Sent status update:', message);
    }
  }

  /**
   * Handle stream track ended event
   */
  function handleStreamEnded() {
    log('🔌 [handleStreamEnded] Stream track ended');
    isStreaming = false;
    sendStatusUpdate('ended', {
      reason: 'track_ended',
    });
  }

  /**
   * Enhanced start streaming with error handling and status updates
   */
  async function start(viewPort) {
    if (!isInitialized) {
      throw new Error('[kazeel][tab-screen-streamer] Not initialized. Call init() first.');
    }

    try {
      await startStreaming(viewPort);

      // Set up stream ended handler
      if (screenStream) {
        const videoTrack = screenStream.getVideoTracks()[0];
        videoTrack.addEventListener('ended', handleStreamEnded);
      }

      return 'SUCCESS';
    } catch (err) {
      sendStatusUpdate('error', {
        error: err.message,
      });
      throw err;
    }
  }

  /**
   * Enhanced stop streaming with cleanup
   */
  async function stop() {
    try {
      await stopStreaming();
      return 'SUCCESS';
    } catch (err) {
      sendStatusUpdate('error', {
        error: err.message,
      });
      throw err;
    }
  }

  /**
   * Get current streaming status
   */
  function getStatus() {
    return {
      initialized: isInitialized,
      streaming: isStreaming,
      socketReady: socket?.readyState === WebSocket.OPEN,
      webrtcState: pc?.connectionState || 'new',
      iceState: pc?.iceConnectionState || 'new',
    };
  }

  /**
   * Cleanup all resources
   */
  function cleanup() {
    log('🧹 [cleanup] Cleaning up tab screen streamer...');

    if (isStreaming) {
      stopStreaming();
    }

    if (pc) {
      pc.close();
      pc = null;
    }

    if (socket) {
      socket.close();
      socket = null;
    }

    if (window.tabStreamerSocket) {
      window.tabStreamerSocket = null;
    }

    isInitialized = false;
    isStreaming = false;
    inputChannel = null;

    log('✅ [cleanup] Cleanup completed');
  }

  // Expose the tab screen streamer interface
  window.tabScreenStreamer = {
    init,
    start,
    stop,
    startStreaming, // Legacy method
    stopStreaming, // Legacy method
    getStatus,
    cleanup,
  };

  // Cleanup on page unload
  window.addEventListener('beforeunload', cleanup);

  log('✅ Tab screen streamer module loaded');
})();
