/**
 * TypeScript definitions for Tab Screen Streamer
 * Browser-injectable script for capturing and streaming tab screen content
 */

export interface TabScreenStreamerViewport {
  width: number;
  height: number;
}

export interface TabScreenStreamerStatus {
  initialized: boolean;
  streaming: boolean;
  socketReady: boolean;
  webrtcState: RTCPeerConnectionState;
  iceState: RTCIceConnectionState;
}

export interface TabScreenStreamerStatusMessage {
  type: 'tab-streaming-status';
  status: 'started' | 'stopped' | 'ended' | 'error';
  timestamp: number;
  viewport?: TabScreenStreamerViewport;
  reason?: string;
  error?: string;
}

export interface TabScreenStreamerClient {
  /**
   * Initialize the tab screen streamer with WebSocket and WebRTC connections
   * @param wsEndpoint - WebSocket endpoint URL
   * @param viewPort - Viewport dimensions
   * @returns Promise that resolves to initialization status
   */
  init(wsEndpoint: string, viewPort: TabScreenStreamerViewport): Promise<'SUCCESS' | 'ALREADY_INITIALIZED'>;

  /**
   * Start streaming the current tab's screen content
   * @param viewPort - Viewport dimensions
   * @returns Promise that resolves to success status
   */
  start(viewPort: TabScreenStreamerViewport): Promise<'SUCCESS'>;

  /**
   * Stop streaming
   * @returns Promise that resolves to success status
   */
  stop(): Promise<'SUCCESS'>;

  /**
   * Legacy method: Start streaming (direct call)
   * @param viewPort - Viewport dimensions
   * @returns Promise that resolves when streaming starts
   */
  startStreaming(viewPort: TabScreenStreamerViewport): Promise<void>;

  /**
   * Legacy method: Stop streaming (direct call)
   * @returns Promise that resolves when streaming stops
   */
  stopStreaming(): Promise<void>;

  /**
   * Get current streaming status
   * @returns Current status object
   */
  getStatus(): TabScreenStreamerStatus;

  /**
   * Cleanup all resources and connections
   */
  cleanup(): void;
}

declare global {
  interface Window {
    /**
     * Tab Screen Streamer - handles tab screen capture and streaming to UI layout
     * Available in target tabs for video streaming operations
     */
    tabScreenStreamer: TabScreenStreamerClient;

    /**
     * WebSocket connection for tab streaming (exposed for external access)
     */
    tabStreamerSocket?: WebSocket;
  }
}

export {};
