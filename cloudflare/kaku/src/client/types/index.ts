import {
  BoundingBox,
  CaptchaDetectorConfig,
  ComparisonResult,
  InputEventData,
  ScreenshotComparisonOptions,
  ScreenshotResult,
  Viewport,
  ImageData as IImageData,
} from '../../common/types/';

// CaptchaDetectorCallback
export type CaptchaDetectorCallback = (
  rgbaBuffer: Uint8Array,
  dimensions: { width: number; height: number },
) => Promise<void>;

/**
 * Persistent CDP Controller interface - handles CDP operations in control tabs
 * Exposed on window.persistentCDPController
 */
export interface PersistentCDPController {
  /**
   * Initialize the persistent CDP controller
   */
  init(): Promise<void>;

  /**
   * Setup browser metrics and viewport configuration
   */
  setupBrowserMetrics(viewport: Viewport): Promise<void>;

  /**
   * Dispatch mouse movement event
   */
  dispatchMouseMove(x: number, y: number): Promise<void>;

  /**
   * Dispatch mouse down event
   */
  dispatchMouseDown(x: number, y: number): Promise<void>;

  /**
   * Dispatch mouse up event
   */
  dispatchMouseUp(x: number, y: number): Promise<void>;

  /**
   * Dispatch mouse click event
   */
  dispatchMouseClick(x: number, y: number): Promise<void>;

  /**
   * Dispatch keyboard event
   */
  dispatchKeyEvent(eventData: InputEventData): Promise<void>;

  /**
   * Insert text at current cursor position
   */
  insertText(text: string): Promise<void>;

  /**
   * Take a screenshot of the current page
   */
  takeScreenshot(): Promise<ScreenshotResult>;

  /**
   * Capture screenshot with grayscale conversion applied
   */
  captureScreenshotWithGrayscale(): Promise<ScreenshotResult>;

  /**
   * Request a new frame generation
   */
  requestNewFrame(): Promise<void>;

  /**
   * Trigger mouse movement to generate UI updates
   */
  triggerMouseMovement(): Promise<void>;

  /**
   * Test connectivity
   */
  ping(): Promise<string>;

  /**
   * Redact PII from the page
   */
  redactPII(): Promise<void>;

  /**
   * Restore PII on the page
   */
  restorePII(): Promise<void>;
}

/**
 * Cross Tab Communicator interface - handles communication between tabs
 * Exposed on window.crossTabCommunicator
 */
export interface CrossTabCommunicator {
  /**
   * Send a message to another tab
   */
  sendMessage(type: string, data?: any): Promise<any>;

  /**
   * Set up message listener
   */
  onMessage(callback: (message: any) => void): void;

  /**
   * Clean up resources
   */
  cleanup(): void;
}

/**
 * Screen Cropper interface for direct client-side usage
 * This represents the actual implementation in the browser context
 */
export interface ScreenCropperClient {
  /**
   * Initialize screen cropper with WebSocket and WebRTC connections
   * Sets up connections but doesn't start streaming
   */
  init(wsEndpoint: string, viewport: Viewport): Promise<string>;

  /**
   * Start screen cropper streaming with captcha detection
   */
  start(viewport: Viewport): Promise<void>;

  /**
   * Stop streaming and clean up resources
   */
  stopStreaming(): void;

  /**
   * Update the crop box for focused capture
   */
  updateCropBox(cropBox: BoundingBox): void;

  /**
   * Start capturing frames for captcha detector
   */
  startCapturingForCaptchaDetector(): void;

  /**
   * Stop capturing frames for captcha detector
   */
  stopCapturingForCaptchaDetector(): void;

  /**
   * Pause frame sending temporarily
   */
  pauseFrameSending(): void;

  /**
   * Resume frame sending
   */
  resumeFrameSending(): void;

  /**
   * Register a callback for captcha detector to receive frames
   */
  registerCaptchaDetectorCallback(callback: CaptchaDetectorCallback): void;
}

/**
 * Captcha Detector interface for direct client-side usage
 * This represents the actual implementation in the browser context
 */
export interface CaptchaDetectorClient {
  /**
   * Initialize the captcha detector with configuration
   */
  initialize(options?: CaptchaDetectorConfig): void;

  /**
   * Clean up resources and event listeners
   */
  cleanup(): void;

  /**
   * Trigger screenshot comparison manually
   */
  triggerScreenshotComparison(): Promise<ComparisonResult>;

  /**
   * Get current configuration
   */
  getConfig(): CaptchaDetectorConfig;

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<CaptchaDetectorConfig>): void;
}

export interface ScreenshotComparisonUtils {
  compareScreenshots: (
    img1: IImageData,
    img2: IImageData,
    options?: ScreenshotComparisonOptions,
  ) => ComparisonResult;
  prepareBufferForComparison: (buffer: Uint8Array, width: number, height: number) => IImageData;
  createTestImage: (width: number, height: number, color?: number[]) => IImageData;
  createImageWithDifferences: (
    baseImage: IImageData,
    diffPixelCount: number,
    diffColor?: number[],
  ) => IImageData;
}

/**
 * Tab Screen Streamer interface for direct client-side usage
 * This represents the actual implementation in the browser context for tab screen streaming
 */
export interface TabScreenStreamerClient {
  /**
   * Initialize the tab screen streamer with WebSocket and WebRTC connections
   * @param wsEndpoint - WebSocket endpoint URL
   * @param viewPort - Viewport dimensions
   * @returns Promise that resolves to initialization status
   */
  init(wsEndpoint: string, viewPort: Viewport): Promise<'SUCCESS' | 'ALREADY_INITIALIZED'>;

  /**
   * Start streaming the current tab's screen content
   * @param viewPort - Viewport dimensions
   * @returns Promise that resolves to success status
   */
  start(viewPort: Viewport): Promise<'SUCCESS'>;

  /**
   * Stop streaming
   * @returns Promise that resolves to success status
   */
  stop(): Promise<'SUCCESS'>;

  /**
   * Legacy method: Start streaming (direct call)
   * @param viewPort - Viewport dimensions
   * @returns Promise that resolves when streaming starts
   */
  startStreaming(viewPort: Viewport): Promise<void>;

  /**
   * Legacy method: Stop streaming (direct call)
   * @returns Promise that resolves when streaming stops
   */
  stopStreaming(): Promise<void>;

  /**
   * Get current streaming status
   * @returns Current status object
   */
  getStatus(): {
    initialized: boolean;
    streaming: boolean;
    socketReady: boolean;
    webrtcState: RTCPeerConnectionState;
    iceState: RTCIceConnectionState;
  };

  /**
   * Cleanup all resources and connections
   */
  cleanup(): void;
}
