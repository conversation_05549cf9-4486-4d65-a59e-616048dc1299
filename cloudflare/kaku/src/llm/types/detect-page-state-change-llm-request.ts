import {PlatformTypes} from "../../ui/constants";
import { PageStatus } from '../../agent/types/agent-state';
import { FormVisionResult } from '../../form-generation/types';

export type DetectPageStateChangeLlmRequest = {
    platform: PlatformTypes;
    prompt: string;
    agentVisionResultState: FormVisionResult,
    screenshot: string;
    skipCache: boolean;
    viewportWidth: number;
    viewportHeight: number;
    version?: string;
}