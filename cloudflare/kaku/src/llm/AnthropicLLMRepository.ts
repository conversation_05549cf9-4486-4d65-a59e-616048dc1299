import { LLMRepository } from './LLMRepository';
import { LLMResponse } from './types/llm-response';
import { Anthropic } from '@anthropic-ai/sdk';
import { TextBlock } from '@anthropic-ai/sdk/resources/messages/messages';
import { LLMRequest } from './types/llm-request';
import {
  DetectPageStateChangeLlmRequest
} from './types/detect-page-state-change-llm-request';
import { generateCacheKeyForStateChangeLLMCalls, generateCacheKeyFromScreenshot } from '../common/utils';

export class AnthropicLLMRepository implements LLMRepository {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL: string) {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }
  async getLLMResponse(llmRequest: LLMRequest): Promise<LLMResponse> {
    const start = Date.now();
    const cacheKey = generateCacheKeyFromScreenshot(llmRequest, llmRequest.version);

    const anthropicClient = new Anthropic({
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      defaultHeaders: {
        'cf-aig-cache-key': cacheKey,
        'cf-aig-skip-cache': llmRequest.skipCache.toString(),
      },
    });
    const response = await anthropicClient.messages.create({
      model: 'claude-opus-4-20250514',
      max_tokens: 1000,
      temperature: 1,
      system: [
        {
          type: 'text',
          text: llmRequest.prompt,
        },
      ],
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'image',
              source: {
                data: llmRequest.screenshot,
                media_type: 'image/webp',
                type: 'base64',
              },
            },
          ],
        },
      ],
    });
    const end = Date.now();
    const duration = end - start;

    return {
      callDuration: duration,
      output_text: (response.content[0] as TextBlock).text,
    };
  }

  async detectStateChangeFromPreviousFormVisionResult(detectPageStateChangeLlmRequest: DetectPageStateChangeLlmRequest): Promise<LLMResponse> {
    const start = Date.now();
    const cacheKey = generateCacheKeyForStateChangeLLMCalls(detectPageStateChangeLlmRequest);

    const anthropicClient = new Anthropic({
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      defaultHeaders: {
        'cf-aig-cache-key': cacheKey,
        'cf-aig-skip-cache': detectPageStateChangeLlmRequest.skipCache.toString(),
      },
    });
    const response = await anthropicClient.messages.create({
      model: 'claude-opus-4-20250514',
      max_tokens: 1000,
      temperature: 1,
      system: [
        {
          type: 'text',
          text: detectPageStateChangeLlmRequest.prompt + JSON.stringify(detectPageStateChangeLlmRequest.agentVisionResultState),
        },
      ],
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'image',
              source: {
                data: detectPageStateChangeLlmRequest.screenshot,
                media_type: 'image/webp',
                type: 'base64',
              },
            }
          ],
        },
      ],
    });
    const end = Date.now();
    const duration = end - start;

    return {
      callDuration: duration,
      output_text: (response.content[0] as TextBlock).text,
    };
  }
}
