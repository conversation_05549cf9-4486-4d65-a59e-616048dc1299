import { WorkflowEntrypoint, WorkflowStep, type WorkflowEvent } from 'cloudflare:workers';
import { PageStateResult } from '../agent/types/extract-result';
import { initRTCSteaming, setupInputFocusListener } from '../browser';
import {
  ConnectionWorkflowState,
  storeConnectionScreenshotToR2,
} from '../common/utils/storeConnectionScreenshotToR2';
import { GeminiLLMRepository } from '../llm/GeminiLLMRepository';
import { LLMService } from '../llm/LLMService';
import { OpenAILLMRepository } from '../llm/OpenAILLMRepository';
import { PlatformTypes } from '../ui/constants';
import { CDPBrowserDataAdapter } from './adapters/CDPBrowserDataAdapter';
import { BrowserStateService } from './BrowserStateService';
import { BrowserServiceFactory, BrowserSession, RemoteBrowserService } from './services';
import {
  ConnectionsWorkflowParams,
  FormSubmissionPayloadSource,
} from './types/ConnectionsWorkflowParams';
import { WorkflowStepName } from './types/WorkflowStepName';
import { getPlatformVersion, K_CUSTOM_VIEWPORT } from './utils/constants';
import { sleep } from './utils/helpers';
import { makeParallelLLMCalls } from '../llm/llm-parallel-calls';
import { CDP } from '../browser/simple-cdp';
import { ClassificationResult } from '../form-generation/types';
import { CoordinatorDOBrowserStateRepository } from './CoordinatorDOBrowserStateRepository';
import { WorkflowStepsManager } from './services/WorkflowStepsManager';
import { BrowserManager } from './services/BrowserManager';
import { Connections } from '../api';
import { FORM_VISION_CLASSIFICATION_PROMPT_V34, FORM_VISION_PROMPT_V6 } from './prompts';

export class ConnectionsWorkflow extends WorkflowEntrypoint<Env, ConnectionsWorkflowParams> {
  private cdp?: CDP;

  //target tab props
  private targetSessionId?: string;
  private targetId?: string;
  private executionContextId?: number;

  private controlTabTargetId?: string;
  private controlTabSessionId?: string;

  private browserSession?: BrowserSession;

  private connectionAgentStub?: DurableObjectStub<Connections>;

  // Logging configuration
  private config = {
    debug: true, // Set to true for verbose logging
  };

  private llmService: LLMService = new LLMService({
    primaryRepo: new GeminiLLMRepository(this.env.GEMINI_API_KEY, this.env.AI_GATEWAY_GEMINI_URL),
    secondaryRepo: new OpenAILLMRepository(this.env.OPENAI_API_KEY, this.env.AI_GATEWAY_OPENAI_URL),
  });

  private browserStateService: BrowserStateService = new BrowserStateService(
    new CoordinatorDOBrowserStateRepository(this.env.CoordinatorDO),
  );
  private browserService: RemoteBrowserService = BrowserServiceFactory.createFromEnvironment(
    this.env,
  );

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][connections-workflow]', ...args);
    }
  }

  private warn(...args: any[]): void {
    console.warn('[kazeel][connections-workflow]', ...args);
  }

  private error(...args: any[]): void {
    console.error('[kazeel][connections-workflow]', ...args);
  }

  async run(event: WorkflowEvent<ConnectionsWorkflowParams>, step: WorkflowStep) {
    this.connectionAgentStub = this.getConnectionAgentStub(event.payload.linkId);

    const browserManager = new BrowserManager({
      env: this.env,
      event: event.payload,
      llmService: this.llmService,
    });

    // Setup-Step 1: Create CDP client
    const { cdp, browserSession } = await browserManager.createCDPSession();

    this.cdp = cdp;
    this.browserSession = browserSession;

    const workflowStepManager = new WorkflowStepsManager({
      env: this.env,
      step: step,
      eventPayload: event.payload,
      errorHandler: async (_) => {
        await this.cleanupResources();
      },
    });

    // Setup-Step 2: Setup browser session
    const { targetId, controlTabTargetId } = await workflowStepManager.runStep(
      WorkflowStepName.SETUP_BROWSER_SESSION,
      async () => {
        if (!this.cdp) throw new Error('CDP client not initialized');
        return await browserManager.setupBrowserSession(this.cdp);
      },
    );

    this.targetId = targetId;
    this.controlTabTargetId = controlTabTargetId;

    // Setup-Step 3: Handle browser two tab architecture
    const { targetSessionId, controlTabSessionId } = await browserManager.setupTwoTabArchitecture({
      cdp: this.cdp,
      controlTabTargetId: this.controlTabTargetId,
      targetId: this.targetId,
      browserSessionWSEndpoint: this.browserSession.wsEndpoint,
    });

    this.targetSessionId = targetSessionId;
    this.controlTabSessionId = controlTabSessionId;

    // Setup-Step 4: Handle browser two tab architecture
    await workflowStepManager.tryRunStep(
      WorkflowStepName.SETUP_VIEWPORT_AND_DEVICE_METRICS,
      async () => {
        if (!this.cdp || !this.targetSessionId) throw new Error('CDP client not initialized');
        await browserManager.ensureViewportSettings({
          cdp: this.cdp,
          targetSessionId: this.targetSessionId,
        });
      },
    );

    await setupInputFocusListener(this.cdp!, this.targetSessionId);

    browserManager
      .setupExecutionContextListener({
        cdp: this.cdp!,
        targetId: this.targetId,
      })
      .then((response) => (this.executionContextId = response));

    await workflowStepManager.tryRunStep(WorkflowStepName.INITIALIZE_SESSION, async () => {
      if (!this.cdp || !this.targetSessionId || !this.controlTabSessionId)
        throw new Error('CDP client not initialized');
      await browserManager.navigateToLoginPage({
        cdp: this.cdp,
        targetSessionId: this.targetSessionId,
        controlTabSessionId: this.controlTabSessionId,
      });
    });

    const screenshot = await workflowStepManager.tryRunStep(
      WorkflowStepName.CAPTURE_SCREENSHOT,
      async () => {
        if (!this.cdp || !this.targetSessionId || !this.executionContextId)
          throw new Error('CDP client not initialized');
        return await browserManager.captureScreenshot({
          cdp: this.cdp,
          targetSessionId: targetSessionId,
          executionContextId: this.executionContextId,
        });
      },
    );

    let isCompleted = false;
    let latestScreenshot = screenshot;

    while (!isCompleted) {
      // Step 3: Generate form with OpenAI
      let pageStateResult = await workflowStepManager.tryRunStep(
        WorkflowStepName.GENERATE_FORM_HTMX,
        async () => {
          return await this.generateFormWithOpenAI(latestScreenshot, event.payload.platformId);
        },
      );

      const pageRequiresExternalFormSubmission = this.checkIfScreenIsATwoFactorAuthScreen(
        pageStateResult.classificationResult,
      );

      if (pageStateResult.classificationResult.screenInfo.screenClass === 'loading-screen') {
        this.log('→ Page is loading, waiting for completion and capturing new screenshot');

        latestScreenshot = await workflowStepManager.tryRunStep(
          WorkflowStepName.SCREENSHOT_AFTER_LOADING,
          async () => {
            if (!this.cdp || !this.targetSessionId || !this.executionContextId)
              throw new Error('CDP client not initialized');
            return await browserManager.captureScreenshot({
              cdp: this.cdp,
              targetSessionId: targetSessionId,
              executionContextId: this.executionContextId,
            });
          },
        );
        this.log('✓ Page loading completed, screenshot updated');
        continue;
      }
      if (pageStateResult.classificationResult.screenInfo.screenClass === 'captcha-screen') {
        //Save the screenshot to R2
        storeConnectionScreenshotToR2(
          this.env.SCREENSHOTS_INBOUND_BUCKET,
          event.payload.userId,
          event.payload.platformId,
          event.payload.sessionId,
          ConnectionWorkflowState.OnCaptcha,
          latestScreenshot,
        );

        await workflowStepManager.tryRunStep(WorkflowStepName.INJECT_SCRIPT, async () => {
          if (!this.cdp || !this.targetSessionId || !this.executionContextId)
            throw new Error('Browser initialization not setup properly');

          await browserManager.injectTargetTabScriptsForCaptcha({
            cdp: this.cdp,
            targetSessionId: this.targetSessionId,
            executionContextId: this.executionContextId,
          });
        });
        const viewport = { width: K_CUSTOM_VIEWPORT.width, height: K_CUSTOM_VIEWPORT.height };

        await workflowStepManager.tryRunStep(
          WorkflowStepName.SEND_CAPTCHA_DETECTION_RESPONSE,
          async () => {
            if (!this.cdp || !this.connectionAgentStub)
              throw new Error('CDP client not initialized');
            await initRTCSteaming(
              { cdpSession: this.cdp },
              this.executionContextId!,
              this.targetSessionId!,
              viewport,
            );
            await this.connectionAgentStub.handleCaptchaDetected(
              this.executionContextId!,
              viewport,
            );
          },
        );

        this.log('→ Waiting for captcha solved notification');
        const captchaSolvedEvent = await workflowStepManager.waitForCaptchaSolvedEvent();

        this.log('✓ Received captcha solved event', {
          differencePercentage: captchaSolvedEvent.payload.differencePercentage,
        });

        if (!this.cdp || !this.targetSessionId || !this.executionContextId) {
          throw new Error('CDP client not initialized');
        }

        await workflowStepManager.tryRunStep(
          WorkflowStepName.WAIT_FOR_PAGE_UPDATE_AFTER_CAPTCHA_COMPLETION,
          async () => {
            if (!this.cdp) throw new Error('CDP client not initialized');

            const configSettings: PlatformDetectionConfig =
              platformDetectionConfigs[event.payload.platformId];

            return await browserManager.waitForPageUpdateAfterSubmission({
              cdp: this.cdp,
              currentFormVisionResult: pageStateResult.extractionResult,
              targetSessionId: targetSessionId,
              configSettings: configSettings,
            });
          },
        );

        latestScreenshot = await browserManager.captureScreenshot({
          cdp: this.cdp,
          targetSessionId: targetSessionId,
          executionContextId: this.executionContextId,
        });
        continue;
      }

      // Step 4: Acknowledge extracted form (Phase 1 + start Phase 2)
      await workflowStepManager.tryRunStep(
        WorkflowStepName.ACKNOWLEDGE_EXTRACTED_FORM,
        async () => {
          await this.acknowledgeExtractedForm(pageStateResult, latestScreenshot);
        },
      );

      if (pageStateResult.classificationResult.screenInfo.authState === 'authenticated') {
        await this.handleOnAuthenticated(event, latestScreenshot);
        isCompleted = true;
        const coordinatorId = this.env.CoordinatorDO.idFromName(event.payload.userId);
        const coordinatorStub = this.env.CoordinatorDO.get(coordinatorId);
        await coordinatorStub.markPlatformConnected(event.payload.linkId);

        //Save the screenshot to R2
        storeConnectionScreenshotToR2(
          this.env.SCREENSHOTS_INBOUND_BUCKET,
          event.payload.userId,
          event.payload.platformId,
          event.payload.sessionId,
          ConnectionWorkflowState.Authenticated,
          latestScreenshot,
        );

        break;
      }

      if (pageRequiresExternalFormSubmission) {
        //We start listening for when a user will authenticate using external methods
        browserManager
          .waitForPageUpdateAfterSubmission({
            cdp: this.cdp,
            currentFormVisionResult: pageStateResult.extractionResult,
            targetSessionId: targetSessionId,
            configSettings: twoFactorChangeDetectionConfig,
          })
          .then(async (pageChangeTypeResponse) => {
            if (pageChangeTypeResponse != null) {
              this.log(`Detected 2-factor state change by type ${pageChangeTypeResponse}`);
              //There was a state change from external source, send a form-submission to let the flow resume
              this.connectionAgentStub!.sendTwoFactorAuthenticationCompletedEvent();
            }
          });
      }

      // Step 5: Wait for user form input
      const formSubmissionEvent = await workflowStepManager.waitForFormSubmittedEvent();

      if (formSubmissionEvent.payload.source == FormSubmissionPayloadSource.PAGE_FORM_SUBMISSION) {
        this.log('Received Form submission event from user filling the form');
        if (pageRequiresExternalFormSubmission) {
          //We need to cancel the listeners for external form submission
          browserManager.cancelActiveBrowserStateChangeListeners();
        }
        // Step 6: Execute form actions
        await workflowStepManager.tryRunStep(WorkflowStepName.EXECUTE_FORM_ACTIONS, async () => {
          if (!this.cdp || !this.targetSessionId) throw new Error('CDP client not initialized');

          await browserManager.executeAIActions(
            this.cdp,
            this.targetSessionId,
            pageStateResult,
            formSubmissionEvent.payload.coordinates,
          );

          await sleep(100);
          return await browserManager.executeFormActions(
            this.cdp,
            this.targetSessionId,
            formSubmissionEvent.payload.payload,
          );
        });

        // Step 7: Wait for page update after submission
        await workflowStepManager.tryRunStep(
          WorkflowStepName.WAIT_FOR_PAGE_UPDATE_AFTER_SUBMISSION,
          async () => {
            if (!this.cdp) throw new Error('CDP client not initialized');

            const configSettings: PlatformDetectionConfig =
              platformDetectionConfigs[event.payload.platformId];

            return await browserManager.waitForPageUpdateAfterSubmission({
              cdp: this.cdp,
              currentFormVisionResult: pageStateResult.extractionResult,
              targetSessionId: targetSessionId,
              configSettings: configSettings,
            });
          },
        );
      } else if (
        formSubmissionEvent.payload.source ==
        FormSubmissionPayloadSource.TWO_FACTOR_AUTHENTICATION_COMPLETION
      ) {
        this.log(
          `Change detected from user acknowledging from external source, skipping form actions step`,
        );
        await this.connectionAgentStub.markWaitingForAgent();
      }

      const screenshot = await workflowStepManager.tryRunStep(
        WorkflowStepName.TAKE_SCREENSHOT,
        async () => {
          if (!this.cdp || !this.targetSessionId || !this.executionContextId)
            throw new Error('CDP client not initialized');
          return await browserManager.captureScreenshot({
            cdp: this.cdp,
            targetSessionId: targetSessionId,
            executionContextId: this.executionContextId,
          });
        },
      );

      latestScreenshot = screenshot;

      //Save the screenshot to R2
      storeConnectionScreenshotToR2(
        this.env.SCREENSHOTS_INBOUND_BUCKET,
        event.payload.userId,
        event.payload.platformId,
        event.payload.sessionId,
        ConnectionWorkflowState.UserFormFilled,
        latestScreenshot,
      );
    }
  }

  private async generateFormWithOpenAI(
    screenshot: string,
    platformId: PlatformTypes,
  ): Promise<PageStateResult> {
    const layoutMetrics = await this.cdp!.Page.getLayoutMetrics(undefined, this.targetSessionId);
    const viewPort = {
      width: layoutMetrics.cssLayoutViewport.clientWidth,
      height: layoutMetrics.cssLayoutViewport.clientHeight,
    };

    const version = getPlatformVersion(platformId, this.env);

    const result = await makeParallelLLMCalls(
      this.llmService,
      {
        platform: platformId,
        screenshot,
        skipCache: this.env.SKIP_CACHE,
        viewportWidth: viewPort.width,
        viewportHeight: viewPort.height,
        version,
      },
      FORM_VISION_PROMPT_V6,
      FORM_VISION_CLASSIFICATION_PROMPT_V34,
    );
    this.log('Parallel LLM calls completed');

    return result;
  }

  private async acknowledgeExtractedForm(
    formData: PageStateResult,
    screenshot: string,
  ): Promise<void> {
    if (!this.connectionAgentStub) {
      throw new Error('Connection agent stub is not initialized');
    }

    await this.connectionAgentStub.onFormStateChange(screenshot, {
      ...formData,
    });
  }

  private checkIfScreenIsATwoFactorAuthScreen(classificationResult: ClassificationResult): boolean {
    return classificationResult.screenInfo.screenClass === 'multi-factor-push-approval-screen';
  }

  private async cleanupResources(): Promise<void> {
    if (this.browserSession?.sessionId) {
      await this.browserService.closeSession(this.browserSession.sessionId);
    } else {
      this.warn('No browser session to close');
    }
  }

  private async handleOnAuthenticated(
    event: WorkflowEvent<ConnectionsWorkflowParams>,
    latestScreenshot: string,
  ) {
    this.log('✓ Workflow completed: user authenticated');
    if (this.cdp) {
      const browserDataAdapter = new CDPBrowserDataAdapter(this.cdp, this.targetSessionId);
      await this.browserStateService.updateBrowserState(
        browserDataAdapter,
        event.payload.userId,
        event.payload.platformId,
      );
    }
    await this.cleanupResources();

    //Save the screenshot to R2
    storeConnectionScreenshotToR2(
      this.env.SCREENSHOTS_INBOUND_BUCKET,
      event.payload.userId,
      event.payload.platformId,
      event.payload.sessionId,
      ConnectionWorkflowState.Authenticated,
      latestScreenshot,
    );
  }

  private getConnectionAgentStub(linkId: string): DurableObjectStub<Connections> {
    const agent = this.env.Connections.idFromName(linkId);
    return this.env.Connections.get(agent);
  }
}
