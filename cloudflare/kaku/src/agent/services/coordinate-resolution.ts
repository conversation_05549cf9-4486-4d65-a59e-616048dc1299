import { LLMService } from '../../llm/LLMService';
import { PlatformTypes } from '../../ui/constants';
import { FormField, FormButton } from '../../form-generation/htmx-generator';
import { sanitizeJSONResponse } from '../../workflow/utils/helpers';

export interface ElementCoordinateRequest {
  fields: FormField[];
  buttons: FormButton[];
}

export interface ElementCoordinateMapping {
  [elementId: string]: {
    x: number;
    y: number;
  };
}

export class CoordinateResolutionService {
  constructor(private llmService: LLMService) {}

  /**
   * New element-based coordinate resolution method with parallel LLM calls
   *
   * This method makes multiple concurrent LLM calls to improve response time and reliability.
   * Each parallel call includes both the LLM request and JSON parsing, so if one call fails
   * due to JSON parsing errors, other calls can still succeed.
   *
   * @param screenshot - Base64 encoded screenshot
   * @param elementRequest - Form fields and buttons to locate
   * @param platform - Platform type (e.g., 'facebook', 'linkedin')
   * @param viewportWidth - Browser viewport width
   * @param viewportHeight - Browser viewport height
   * @param maxRetries - Maximum number of retry attempts (default: 3)
   * @param enableParallelCalls - Whether to enable parallel calls (default: true)
   * @param noOfConcurrentCalls - Number of concurrent calls to make (default: 2)
   * @returns Promise resolving to coordinate mapping for all elements
   */
  async resolveElementCoordinates(
    screenshot: string,
    elementRequest: ElementCoordinateRequest,
    platform: PlatformTypes,
    viewportWidth: number,
    viewportHeight: number,
    maxRetries: number = 3,
    enableParallelCalls: boolean = true,
    noOfConcurrentCalls: number = 2,
  ): Promise<ElementCoordinateMapping> {
    const elementsContext = this.buildElementsContext(elementRequest);
    const prompt = this.buildElementCoordinateResolutionPrompt(elementsContext);

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const coordinateResults = await this.makeParallelCoordinateResolutionCalls(
          {
            platform,
            prompt,
            screenshot,
            skipCache: true,
            viewportWidth,
            viewportHeight,
          },
          elementsContext,
          enableParallelCalls,
          noOfConcurrentCalls,
        );

        return coordinateResults;
      } catch (error) {
        lastError = error as Error;
        console.error(`Element coordinate resolution attempt ${attempt} failed:`, error);

        if (attempt === maxRetries) {
          throw new Error(
            `Failed to resolve element coordinates after ${maxRetries} attempts for elements: ${elementsContext.map((e) => e.id).join(', ')}. Last error: ${lastError.message}`,
          );
        }

        // Wait before retry (exponential backoff)
        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    throw lastError || new Error('Element coordinate resolution failed');
  }

  /**
   * Makes parallel LLM calls for coordinate resolution with JSON parsing included in the race
   */
  private async makeParallelCoordinateResolutionCalls(
    llmRequest: {
      platform: PlatformTypes;
      prompt: string;
      screenshot: string;
      skipCache: boolean;
      viewportWidth: number;
      viewportHeight: number;
    },
    elementsContext: Array<{ id: string; type: string; label: string; actiontype: string }>,
    enableParallelCalls: boolean,
    noOfConcurrentCalls: number,
  ): Promise<ElementCoordinateMapping> {
    const numberOfCalls = enableParallelCalls ? noOfConcurrentCalls : 1;

    const coordinateResolutionCalls = Array.from({ length: numberOfCalls }, async () => {
      const response = await this.llmService.getLLMResponse(llmRequest);
      const coordinateResults = sanitizeJSONResponse<ElementCoordinateMapping>(
        response.output_text,
      );
      return this.validateElementCoordinates(coordinateResults, elementsContext);
    });

    return await this.getResponseFromMultipleCoordinateCalls(coordinateResolutionCalls);
  }

  /**
   * Races multiple coordinate resolution calls and returns the first successful result
   */
  private async getResponseFromMultipleCoordinateCalls(
    calls: Promise<ElementCoordinateMapping>[],
  ): Promise<ElementCoordinateMapping> {
    return new Promise((resolve, reject) => {
      let rejections: any[] = [];
      let pending = calls.length;

      calls.forEach((call) => {
        call.then(resolve).catch((err) => {
          rejections.push(err);
          pending--;
          if (pending === 0) {
            reject(new AggregateError(rejections, 'All coordinate resolution calls failed'));
          }
        });
      });
    });
  }

  private buildElementsContext(elementRequest: ElementCoordinateRequest): Array<{
    id: string;
    type: string;
    label: string;
    actiontype: string;
    fieldType?: string;
    optionIndex?: number;
  }> {
    const elements: Array<{
      id: string;
      type: string;
      label: string;
      actiontype: string;
      fieldType?: string;
    }> = [];

    elementRequest.fields.forEach((field) => {
      if (field.fieldControlType === 'radiogroup' && field.options && field.options.length > 0) {
        field.options.forEach((option) => {
          elements.push({
            id: `${field.id}-${option.value}`,
            type: 'radio-option',
            label: option.label,
            actiontype: field.actiontype,
            fieldType: 'radiogroup',
          });
        });
      } else {
        elements.push({
          id: field.id,
          type: 'field',
          label: field.label,
          actiontype: field.actiontype,
          fieldType: field.fieldControlType,
        });
      }
    });

    // Add buttons
    elementRequest.buttons.forEach((button) => {
      elements.push({
        id: button.id,
        type: 'button',
        label: button.label,
        actiontype: button.actiontype,
      });
    });

    return elements.sort((a, b) => {
      // Find the order for element a
      const aElement = [...elementRequest.fields, ...elementRequest.buttons].find(
        (el) => el.id === a.id || a.id.startsWith(`${el.id}-`),
      );
      const bElement = [...elementRequest.fields, ...elementRequest.buttons].find(
        (el) => el.id === b.id || b.id.startsWith(`${el.id}-`),
      );

      const aOrder = aElement?.order ?? 999;
      const bOrder = bElement?.order ?? 999;

      return aOrder - bOrder;
    });
  }

  private buildElementCoordinateResolutionPrompt(
    elementsContext: Array<{ id: string; type: string; label: string; actiontype: string }>,
  ): string {
    const elementsJson = JSON.stringify(elementsContext, null, 2);

    return `Find pixel coordinates for UI elements in this screenshot.

Elements to locate:
${elementsJson}

Rules:
- Input fields: center of text box
- Buttons: center of button
- Radio buttons: can also point to the label
- Coordinates must be positive integers

You MUST respond with ONLY this JSON format (no other text):
{
  "password": {"x": 400, "y": 250},
  "login-button": {"x": 400, "y": 320}
}

Include ALL elements. If unsure, estimate typical form positions.

JSON:`;
  }

  private validateElementCoordinates(
    coordinateResults: ElementCoordinateMapping,
    elementsContext: Array<{ id: string; type: string; label: string; actiontype: string }>,
  ): ElementCoordinateMapping {
    // Validate that all expected elements have coordinates
    for (const element of elementsContext) {
      const elementCoord = coordinateResults[element.id];

      if (!elementCoord) {
        throw new Error(`Missing coordinates for element: ${element.id}`);
      }
    }

    return coordinateResults;
  }
}
