/**
 * Template engine for the UI using Hono HTML helper
 */
import { html, raw } from 'hono/html';
import { AgentState } from '../agent/types';
import { ProcessedError } from '../common/error/types';
import {
  KazeelPlatformLogo,
  KazeelPlatformLogoConnected,
  LiveView,
  LLMFormContainer,
  Loading,
  ServiceContext,
} from './components';
import { ErrorDisplay } from './components/error-display';

import { generateHtmxForm } from '../form-generation/utils/form-generator';
import { platformDetails, PlatformTypes } from './constants';
import { HTMLReturnType } from './types';

export interface TemplateContext extends ServiceContext {}
export class TemplateEngine {
  /** Render LLM-generated form */
  renderLLMForm(formContent: string, context: TemplateContext): HTMLReturnType {
    return LLMFormContainer({ ...context, formContent });
  }

  /** Render live view */
  renderLiveView(context: TemplateContext): HTMLReturnType {
    return LiveView(context);
  }

  /** Render loading indicator */
  renderLoading(loadingText?: string, platform?: string): HTMLReturnType {
    const platformInfo = platform ? platformDetails[platform] : undefined;
    return Loading({
      loadingText,
      platformLogo: platformInfo?.logo,
      platformName: platformInfo?.name,
    });
  }

  /** Render completed state */
  renderCompleted(platform: string): HTMLReturnType {
    const platformInfo = platformDetails[platform];

    if (!platformInfo) {
      const errorData: ProcessedError = {
        userMessage: 'Platform not supported',
        shouldReplaceCard: true,
        logLevel: 'error',
        errorCode: 'SYSTEM_ERROR',
      };
      return ErrorDisplay(errorData);
    }

    return html`<div class="p-6 pb-10 content-center">
      <div class="flex flex-col items-center justify-center h-full">
        <div class="flex-1 content-center">
          ${KazeelPlatformLogoConnected({
            platformLogo: platformInfo.logo,
            platformName: platformInfo.name,
          })}

          <div class="p-2 text-center">
            <h2 class="text-xl font-semibold my-4">You’re Connected to ${platformInfo.name}</h2>
            <p class="text-sm text-surface-on-variant">Your connection is successful!</p>
            <p class="text-sm text-surface-on-variant">You can close this tab now.</p>
          </div>
        </div>

        <div class="mt-14 flex items-center justify-center">
          <a href="asda" class="text-xs text-[#7A757F] underline">Privacy Policy</a>
          <span class="text-xs text-[#7A757F] mx-[2px]">•</span>
          <a href="asda" class="text-xs text-[#7A757F] underline">English (United States)</a>
        </div>
      </div>
    </div>`;
  }

  /** Render error state */
  renderError(message: string): HTMLReturnType {
    return html` <div class="text-center p-4 bg-red-200 rounded-lg">Error: ${message}</div> `;
  }

  /** Render complex error state */
  renderComplexError(agentState: AgentState): HTMLReturnType {
    const errorData: ProcessedError = {
      userMessage: agentState.errorMessage || 'An error occurred',
      shouldReplaceCard: true,
      logLevel: 'error',
      errorCode: 'SYSTEM_ERROR',
    };
    return ErrorDisplay(errorData);
  }

  /** Render waiting-for-human form */
  renderWaitingForHumanForm(agentState: AgentState, platform: PlatformTypes): HTMLReturnType {
    const platformInfo = platformDetails[platform];

    if (!platformInfo) {
      const errorData: ProcessedError = {
        userMessage: 'Platform not supported',
        shouldReplaceCard: true,
        logLevel: 'error',
        errorCode: 'SYSTEM_ERROR',
      };
      return ErrorDisplay(errorData);
    }

    if (agentState.interactivity?.status === 'paused') {
      const updateUI = html`
        <div id="captcha-status" hx-swap-oob="innerHTML">
          <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4" role="alert">
            <p class="font-bold">Change Detected</p>
            <p>Changes detected in the captcha. Verifying with LLM...</p>
          </div>
        </div>
      `;
      return updateUI;
    }
    return html`<div
      id="form-container"
      hx-swap-oob="innerHTML"
      class="w-full md:h-auto flex flex-col justify-center items-center p-8"
    >
      <img
        id="live-view-img"
        src="https://ishadeed.com/assets/fb-login/fb-new-login.png"
        alt="Facebook Login Page"
        class="h-auto min-h-60 hidden"
      />

      <div class="mb-8">
        ${KazeelPlatformLogo({
          platformLogo: platformInfo.logo,
          platformName: platformInfo.name,
          variant: 'overlapping',
        })}
      </div>

      <div class="text-start w-full">
        <h1 class="text-xl font-semibold mb-2">
          ${agentState.pageStateResult?.classificationResult.screenInfo.title || 'Sign In'}
        </h1>
      </div>

      ${agentState.pageStateResult
        ? raw(
            generateHtmxForm(
              agentState.pageStateResult.extractionResult,
              agentState.pageStateResult.classificationResult,
            ),
          )
        : null}

      <script>
        function toggleLiveView() {
          const img = document.getElementById('live-view-img');
          const button = document.getElementById('toggle-live-view');

          if (img.classList.contains('hidden')) {
            img.classList.remove('hidden');
            button.textContent = 'Hide Live View';
          } else {
            img.classList.add('hidden');
            button.textContent = 'Show Live View';
          }
        }
      </script>
    </div>`;
  }

  generateContent(agentState: AgentState, platform: PlatformTypes) {
    if (
      agentState.initializationStatus === 'initial' ||
      agentState.initializationStatus === 'in_progress'
    ) {
      return this.renderLoading('Setting up your session...', platform);
    }

    if (agentState.initializationStatus === 'failed') {
      const errorData: ProcessedError = {
        userMessage: agentState.errorMessage || 'Failed to set up your session. Please try again.',
        shouldReplaceCard: true,
        logLevel: 'error',
        errorCode: 'SYSTEM_ERROR',
      };
      return ErrorDisplay(errorData);
    }
    switch (agentState.status) {
      case 'waiting-for-human':
        return this.renderWaitingForHumanForm(agentState, platform);
      case 'completed':
        return this.renderCompleted(platform);
      case 'error':
        return this.renderComplexError(agentState);
      default:
        return this.renderLoading(undefined, platform);
    }
  }
}

export const templateEngine = new TemplateEngine();
