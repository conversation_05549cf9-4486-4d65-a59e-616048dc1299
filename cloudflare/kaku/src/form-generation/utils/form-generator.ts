/**
 * Isolated form generation utilities
 */

import { htmxFormGenerator } from '../htmx-generator';
import { ClassificationResult, ScreenClass } from '../types';
import { ExtractionResult } from '../types/form-interfaces';

/**
 * Generate HTMX form markup from FormVisionResult
 * This is an isolated function that can be called independently
 */
export function generateHtmxForm(
  extractionResult: ExtractionResult,
  classificationResult: ClassificationResult,
): string {
  return htmxFormGenerator.generateForm(extractionResult, classificationResult);
}
