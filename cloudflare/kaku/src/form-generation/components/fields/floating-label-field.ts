/**
 * Floating label field component
 */

import type { FormField } from '../../types';

/**
 * Check if field type should use floating label
 */
export function shouldUseFloatingLabel(type: string): boolean {
  return ['text', 'password', 'email', 'number'].includes(type);
}

/**
 * Generate floating label field markup
 */
export function generateFloatingLabelField(field: FormField): string {
  return `
    <div class="input-container">
      <div class="floating-input-wrapper">
        <input
          class="floating-input"
          name="${field.id}"
          type="${field.fieldControlType}"
          placeholder=""
          id="${field.id}"
        >
        <label class="floating-label" for="${field.id}">${field.label}</label>
      </div>
    </div>
  `;
}
