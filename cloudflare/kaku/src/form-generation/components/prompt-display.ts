/**
 * Prompt display component
 */

/**
 * Generate prompt display markup for verification codes and contextual information
 */
export function generatePromptDisplay(
  instruction: string | null,
  verificationCode: string | null,
  displayInstruction: boolean = true,
): string {
  if (!instruction && !verificationCode) return '';

  const elements: string[] = [];

  if (displayInstruction && instruction) {
    elements.push(`
      <div class="mb-2">${instruction}</div>
    `);
  }

  if (verificationCode) {
    elements.push(`
        <div class="verification-code-value">${verificationCode}</div>
    `);
  }
  return `<div class="prompt-container">${elements.join('')}</div>`;
}
