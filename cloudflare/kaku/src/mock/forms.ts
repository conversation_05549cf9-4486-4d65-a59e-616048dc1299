import { PageStateResult } from '../agent/types/extract-result';
import { ExtractionResult } from '../form-generation/types/form-interfaces';
import { ClassificationResult } from '../form-generation/types/classification-interfaces';

// Mock extraction result for GitHub login
const githubExtractionResult: ExtractionResult = {
  screenInfo: {
    title: 'Sign in to GitHub',
    description: '',
    instruction: 'Please enter your GitHub credentials',
    authState: 'not-authenticated',
    errors: [],
  },
  controls: {
    fields: [
      {
        id: 'login',
        order: 1,
        label: 'Username or email address',
        fieldControlType: 'text',
        actiontype: 'fill',
        name: 'login',
        checked: false,
      },
      {
        id: 'password',
        order: 2,
        label: 'Password',
        fieldControlType: 'password',
        actiontype: 'fill',
        name: 'password',
        checked: false,
      },
    ],
    buttons: [
      {
        id: 'signin',
        order: 3,
        label: 'Sign in',
        variant: 'primary',
        type: 'submit',
        actiontype: 'click',
      },
    ],
  },
};

// Mock classification result for GitHub login
const githubClassificationResult: ClassificationResult = {
  screenInfo: {
    title: 'Sign in to GitHub',
    description: 'GitHub authentication page',
    authState: 'not-authenticated',
    classificationReasoning: 'Login form with username/email and password fields detected',
    screenClass: 'other',
    verificationCode: null,
    errors: [],
  },
};

export const githubLoginForm: PageStateResult = {
  // Raw data from both prompts
  extractionResult: githubExtractionResult,
  classificationResult: githubClassificationResult,
};

// Separate coordinate mapping for GitHub login form
export const githubLoginFormCoordinates = {
  login: { x: 378, y: 220 },
  password: { x: 378, y: 298 },
  signin: { x: 395, y: 345 },
};

// Mock extraction result for device verification
const verifyDeviceExtractionResult: ExtractionResult = {
  screenInfo: {
    title: 'Device verification',
    description:
      'Please enter the authentication code sent to your email to verify your device. The code will expire at 3:27PM EAT.',
    instruction: 'Enter the verification code from your email',
    authState: 'not-authenticated',
    errors: [],
  },
  controls: {
    fields: [
      {
        id: 'otp',
        order: 1,
        label: 'Device Verification Code',
        fieldControlType: 'text',
        actiontype: 'fill',
        name: 'otp',
        checked: false,
      },
    ],
    buttons: [
      {
        id: 'verify',
        order: 2,
        label: 'Verify',
        variant: 'primary',
        type: 'submit',
        actiontype: 'click',
      },
    ],
  },
};

// Mock classification result for device verification
const verifyDeviceClassificationResult: ClassificationResult = {
  screenInfo: {
    title: 'Device verification',
    description: 'Multi-factor authentication verification screen',
    authState: 'not-authenticated',
    classificationReasoning:
      'Screen contains verification code input field for multi-factor authentication',
    screenClass: 'multi-factor-verification-screen',
    verificationCode: null,
    errors: [],
  },
};

export const verifyDeviceForm: PageStateResult = {
  // Raw data from both prompts
  extractionResult: verifyDeviceExtractionResult,
  classificationResult: verifyDeviceClassificationResult,
};

// Separate coordinate mapping for device verification form
export const verifyDeviceFormCoordinates = {
  otp: { x: 392, y: 419 },
  verify: { x: 392, y: 465 },
};

// Mock extraction result for authenticated state
const authenticatedExtractionResult: ExtractionResult = {
  screenInfo: {
    title: 'Authenticated',
    description: 'User is successfully authenticated',
    instruction: 'You are now logged in',
    authState: 'authenticated',
    errors: [],
  },
  controls: {
    fields: [],
    buttons: [],
  },
};

// Mock classification result for authenticated state
const authenticatedClassificationResult: ClassificationResult = {
  screenInfo: {
    title: 'Authenticated',
    description: 'User is successfully authenticated',
    authState: 'authenticated',
    classificationReasoning: 'User has successfully completed authentication',
    screenClass: 'other',
    verificationCode: null,
    errors: [],
  },
};

export const authenticatedForm: PageStateResult = {
  // Raw data from both prompts
  extractionResult: authenticatedExtractionResult,
  classificationResult: authenticatedClassificationResult,
};
