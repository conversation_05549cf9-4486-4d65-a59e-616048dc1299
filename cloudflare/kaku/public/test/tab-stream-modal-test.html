<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Stream Modal Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="/css/video-layout.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="flex items-center justify-center p-8">
    <div class="bg-white rounded-lg shadow-xl p-8 max-w-md w-full">
        <h1 class="text-2xl font-bold text-gray-800 mb-4">Tab Stream Modal Test</h1>
        <p class="text-gray-600 mb-6">
            This page demonstrates the tab stream modal functionality. Click the button below to test the modal.
        </p>
        
        <div class="space-y-4">
            <button 
                id="test-open-modal" 
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
                Open Tab Stream Modal
            </button>
            
            <button 
                id="test-inject-script" 
                class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
                Inject Tab Screen Streamer
            </button>
            
            <div id="status" class="text-sm text-gray-500 p-3 bg-gray-50 rounded border">
                Status: Ready
            </div>
        </div>
    </div>

    <!-- Tab Stream Modal (copied from layout.ts) -->
    <div
        id="tab-stream-modal"
        class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden items-center justify-center"
        style="display: none;"
    >
        <div class="relative w-full h-full max-w-7xl max-h-screen p-4">
            <!-- Modal Header -->
            <div class="absolute top-4 left-4 right-4 z-60 flex justify-between items-center">
                <div class="flex items-center text-white">
                    <div class="w-8 h-8 mr-3 flex-shrink-0">
                        <svg class="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold">Tab Screen Stream</h2>
                        <p class="text-sm opacity-75">Live view of the current tab</p>
                    </div>
                </div>
                <button
                    id="close-tab-stream-modal"
                    class="text-white hover:text-gray-300 transition-colors p-2 rounded-full hover:bg-white hover:bg-opacity-10"
                    aria-label="Close tab stream"
                >
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="w-full h-full pt-16 pb-4">
                <div id="tab-stream-video-container" class="w-full h-full bg-gray-900 rounded-lg overflow-hidden relative">
                    <!-- Tab Stream Video will be inserted here -->
                    <div
                        id="tab-stream-loading"
                        class="absolute inset-0 flex items-center justify-center bg-gray-900 text-white"
                    >
                        <div class="text-center">
                            <div class="animate-spin mx-auto mb-4 h-8 w-8 text-blue-400">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                            <p class="text-lg font-medium">Initializing Tab Stream...</p>
                            <p class="text-sm opacity-75 mt-2">Please allow screen sharing when prompted</p>
                        </div>
                    </div>
                    
                    <!-- Error State -->
                    <div
                        id="tab-stream-error"
                        class="absolute inset-0 items-center justify-center bg-gray-900 text-white hidden"
                    >
                        <div class="text-center max-w-md">
                            <div class="text-red-400 mb-4">
                                <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <p class="text-lg font-medium mb-2">Stream Error</p>
                            <p id="tab-stream-error-message" class="text-sm opacity-75 mb-4">Failed to start tab streaming</p>
                            <button
                                id="retry-tab-stream"
                                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                            >
                                Retry
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="absolute bottom-4 left-4 right-4 flex justify-between items-center text-white text-sm">
                <div class="flex items-center space-x-4">
                    <span id="tab-stream-status" class="opacity-75">Disconnected</span>
                    <div id="tab-stream-quality" class="opacity-75">Quality: Auto</div>
                </div>
                <div class="flex items-center space-x-2">
                    <button
                        id="toggle-tab-stream"
                        class="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                    >
                        Stop Stream
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Stream Trigger Button -->
    <button
        id="open-tab-stream-modal"
        class="fixed bottom-4 right-4 z-40 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-105"
        title="View Tab Stream"
        style="display: none;"
    >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
        </svg>
    </button>

    <script>
        // Test functionality
        let tabStreamModal = null;
        
        function updateStatus(message) {
            const status = document.getElementById('status');
            if (status) {
                status.textContent = `Status: ${message}`;
            }
        }

        function initTestModal() {
            tabStreamModal = document.getElementById('tab-stream-modal');
            const openButton = document.getElementById('open-tab-stream-modal');
            const closeButton = document.getElementById('close-tab-stream-modal');
            const testOpenButton = document.getElementById('test-open-modal');
            const testInjectButton = document.getElementById('test-inject-script');

            // Show the floating trigger button
            if (openButton) {
                openButton.style.display = 'block';
            }

            // Test button to open modal
            if (testOpenButton) {
                testOpenButton.addEventListener('click', () => {
                    updateStatus('Opening modal...');
                    openModal();
                });
            }

            // Test button to inject script
            if (testInjectButton) {
                testInjectButton.addEventListener('click', () => {
                    updateStatus('Injecting tab screen streamer script...');
                    injectTabScreenStreamer();
                });
            }

            // Open modal button
            if (openButton) {
                openButton.addEventListener('click', openModal);
            }

            // Close modal button
            if (closeButton) {
                closeButton.addEventListener('click', closeModal);
            }

            // Close on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && tabStreamModal && !tabStreamModal.classList.contains('hidden')) {
                    closeModal();
                }
            });

            // Close on backdrop click
            if (tabStreamModal) {
                tabStreamModal.addEventListener('click', (e) => {
                    if (e.target === tabStreamModal) {
                        closeModal();
                    }
                });
            }

            updateStatus('Test page initialized');
        }

        function openModal() {
            if (!tabStreamModal) return;
            
            updateStatus('Modal opened');
            tabStreamModal.style.display = 'flex';
            tabStreamModal.classList.remove('hidden');
            
            // Show loading state
            const loading = document.getElementById('tab-stream-loading');
            const error = document.getElementById('tab-stream-error');
            if (loading) loading.style.display = 'flex';
            if (error) error.style.display = 'none';
            
            // Simulate initialization
            setTimeout(() => {
                updateStatus('Simulating tab stream initialization...');
                const statusElement = document.getElementById('tab-stream-status');
                if (statusElement) statusElement.textContent = 'Initializing...';
            }, 500);
        }

        function closeModal() {
            if (!tabStreamModal) return;
            
            updateStatus('Modal closed');
            tabStreamModal.style.display = 'none';
            tabStreamModal.classList.add('hidden');
        }

        function injectTabScreenStreamer() {
            // Simulate script injection
            const script = document.createElement('script');
            script.src = '/client/tab-screen-streamer.min.js';
            script.onload = () => {
                updateStatus('Tab screen streamer script loaded');
                console.log('Tab screen streamer available:', !!window.tabScreenStreamer);
            };
            script.onerror = () => {
                updateStatus('Failed to load tab screen streamer script');
            };
            document.head.appendChild(script);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initTestModal);
    </script>
</body>
</html>
