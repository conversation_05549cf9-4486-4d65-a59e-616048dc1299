<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Stream Integration Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="p-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-xl p-8 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">Tab Stream Integration Test</h1>
            <p class="text-gray-600 mb-6">
                This page tests the complete tab screen streaming integration including script injection, 
                WebRTC setup, and streaming functionality.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Control Panel -->
                <div class="space-y-4">
                    <h2 class="text-xl font-semibold text-gray-800">Control Panel</h2>
                    
                    <button 
                        id="inject-script" 
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                    >
                        1. Inject Tab Screen Streamer
                    </button>
                    
                    <button 
                        id="init-streamer" 
                        class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                        disabled
                    >
                        2. Initialize Streamer
                    </button>
                    
                    <button 
                        id="start-streaming" 
                        class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                        disabled
                    >
                        3. Start Streaming
                    </button>
                    
                    <button 
                        id="stop-streaming" 
                        class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                        disabled
                    >
                        4. Stop Streaming
                    </button>
                    
                    <button 
                        id="get-status" 
                        class="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                        disabled
                    >
                        Get Status
                    </button>
                </div>
                
                <!-- Status Panel -->
                <div class="space-y-4">
                    <h2 class="text-xl font-semibold text-gray-800">Status</h2>
                    
                    <div id="status-display" class="bg-gray-50 border rounded-lg p-4 h-64 overflow-y-auto">
                        <div class="text-sm text-gray-600">Ready to start testing...</div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="bg-blue-50 p-2 rounded">
                            <span class="font-medium">Script:</span>
                            <span id="script-status" class="text-red-600">Not Loaded</span>
                        </div>
                        <div class="bg-green-50 p-2 rounded">
                            <span class="font-medium">Init:</span>
                            <span id="init-status" class="text-red-600">Not Initialized</span>
                        </div>
                        <div class="bg-purple-50 p-2 rounded">
                            <span class="font-medium">Stream:</span>
                            <span id="stream-status" class="text-red-600">Not Streaming</span>
                        </div>
                        <div class="bg-gray-50 p-2 rounded">
                            <span class="font-medium">WebRTC:</span>
                            <span id="webrtc-status" class="text-red-600">Disconnected</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Test Content -->
        <div class="bg-white rounded-lg shadow-xl p-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Test Content</h2>
            <p class="text-gray-600 mb-4">
                This content will be captured by the tab screen streamer when streaming is active.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-red-100 p-4 rounded-lg">
                    <h3 class="font-semibold text-red-800">Red Section</h3>
                    <p class="text-red-600">This is a red colored section for visual testing.</p>
                </div>
                <div class="bg-green-100 p-4 rounded-lg">
                    <h3 class="font-semibold text-green-800">Green Section</h3>
                    <p class="text-green-600">This is a green colored section for visual testing.</p>
                </div>
                <div class="bg-blue-100 p-4 rounded-lg">
                    <h3 class="font-semibold text-blue-800">Blue Section</h3>
                    <p class="text-blue-600">This is a blue colored section for visual testing.</p>
                </div>
            </div>
            
            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h3 class="font-semibold text-yellow-800 mb-2">Animation Test</h3>
                <div class="w-16 h-16 bg-yellow-400 rounded-full animate-bounce mx-auto"></div>
            </div>
        </div>
    </div>

    <script>
        // Test WebSocket endpoint (replace with actual endpoint)
        const WS_ENDPOINT = 'ws://localhost:8080/stream';
        const VIEWPORT = { width: 1366, height: 768 };
        
        let statusLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            statusLog.push(logEntry);
            
            const statusDisplay = document.getElementById('status-display');
            if (statusDisplay) {
                statusDisplay.innerHTML = statusLog.map(entry => 
                    `<div class="mb-1 ${type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-gray-700'}">${entry}</div>`
                ).join('');
                statusDisplay.scrollTop = statusDisplay.scrollHeight;
            }
            
            console.log(logEntry);
        }
        
        function updateStatus(element, status, isSuccess = false) {
            const el = document.getElementById(element);
            if (el) {
                el.textContent = status;
                el.className = isSuccess ? 'text-green-600' : 'text-red-600';
            }
        }
        
        function enableButton(id) {
            const btn = document.getElementById(id);
            if (btn) btn.disabled = false;
        }
        
        function disableButton(id) {
            const btn = document.getElementById(id);
            if (btn) btn.disabled = true;
        }
        
        // Test functions
        async function injectScript() {
            try {
                log('Injecting tab screen streamer script...');
                
                const script = document.createElement('script');
                script.src = '/client/tab-screen-streamer.min.js';
                
                await new Promise((resolve, reject) => {
                    script.onload = () => {
                        log('✅ Tab screen streamer script loaded successfully', 'success');
                        updateStatus('script-status', 'Loaded', true);
                        enableButton('init-streamer');
                        enableButton('get-status');
                        resolve();
                    };
                    script.onerror = () => {
                        log('❌ Failed to load tab screen streamer script', 'error');
                        reject(new Error('Script load failed'));
                    };
                    document.head.appendChild(script);
                });
                
                // Check if the global object is available
                if (window.tabScreenStreamer) {
                    log('✅ window.tabScreenStreamer is available', 'success');
                } else {
                    log('❌ window.tabScreenStreamer is not available', 'error');
                }
                
            } catch (error) {
                log(`❌ Script injection failed: ${error.message}`, 'error');
            }
        }
        
        async function initStreamer() {
            try {
                log('Initializing tab screen streamer...');
                
                if (!window.tabScreenStreamer) {
                    throw new Error('Tab screen streamer not available');
                }
                
                const result = await window.tabScreenStreamer.init(WS_ENDPOINT, VIEWPORT);
                log(`✅ Initialization result: ${result}`, 'success');
                updateStatus('init-status', 'Initialized', true);
                enableButton('start-streaming');
                
            } catch (error) {
                log(`❌ Initialization failed: ${error.message}`, 'error');
            }
        }
        
        async function startStreaming() {
            try {
                log('Starting tab screen streaming...');
                
                const result = await window.tabScreenStreamer.start(VIEWPORT);
                log(`✅ Streaming started: ${result}`, 'success');
                updateStatus('stream-status', 'Streaming', true);
                enableButton('stop-streaming');
                disableButton('start-streaming');
                
            } catch (error) {
                log(`❌ Start streaming failed: ${error.message}`, 'error');
            }
        }
        
        async function stopStreaming() {
            try {
                log('Stopping tab screen streaming...');
                
                const result = await window.tabScreenStreamer.stop();
                log(`✅ Streaming stopped: ${result}`, 'success');
                updateStatus('stream-status', 'Stopped', false);
                enableButton('start-streaming');
                disableButton('stop-streaming');
                
            } catch (error) {
                log(`❌ Stop streaming failed: ${error.message}`, 'error');
            }
        }
        
        async function getStatus() {
            try {
                if (!window.tabScreenStreamer) {
                    throw new Error('Tab screen streamer not available');
                }
                
                const status = window.tabScreenStreamer.getStatus();
                log(`📊 Status: ${JSON.stringify(status, null, 2)}`);
                
                updateStatus('init-status', status.initialized ? 'Initialized' : 'Not Initialized', status.initialized);
                updateStatus('stream-status', status.streaming ? 'Streaming' : 'Not Streaming', status.streaming);
                updateStatus('webrtc-status', status.webrtcState || 'Unknown', status.webrtcState === 'connected');
                
            } catch (error) {
                log(`❌ Get status failed: ${error.message}`, 'error');
            }
        }
        
        // Event listeners
        document.getElementById('inject-script').addEventListener('click', injectScript);
        document.getElementById('init-streamer').addEventListener('click', initStreamer);
        document.getElementById('start-streaming').addEventListener('click', startStreaming);
        document.getElementById('stop-streaming').addEventListener('click', stopStreaming);
        document.getElementById('get-status').addEventListener('click', getStatus);
        
        // Initial log
        log('Tab Stream Integration Test initialized');
    </script>
</body>
</html>
