import { imageToB<PERSON>64 } from '../common/ImageHelpers';
import { encode } from 'gpt-tokenizer';
import { GeminiLLMRepository } from '../../src/llm/GeminiLLMRepository';
import { monolithicFormPromptInstructions } from '../../src/workflow/utils/constants';
import { LLMResponse } from '../../src/llm/types/llm-response';
import { EXTRACTION_RESPONSE_SCHEMA } from '../../src/llm/schemas/response-schemas';

// Commenting this to unblock deployment
import { fileURLToPath } from 'url';
import path from 'path';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const GEMINI_API_KEY = 'AIzaSyDzh7xuznZzDi0c0DCYegXqGDH3UxFF3DQ'; // TODO: ADD API KEY
const DEFAULT_ITERATIONS = 5;

const TEST_PROMPTS = {
  simple: `
Is the user authenticated? Answer yes or no, nothing else
  `,
  medium: `
Provide an HTMX code that renders only the controls needed to complete that form.
### HTMX Form Requirements:
- Main form element: <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML">
- Primary Login button MUST be a <button> element and MUST include: hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click". This is a critical requirement.
- ALL other buttons must also include: hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML".
- ALL input fields in the HTMX form must be empty even if the screenshot shows them filled
- Strictly focus on elements that are needed to complete the form to login. Skip buttons like "forget password"

### CSS Classes (use these exact classes):
- form-container
- input-container: wrapper for all input types
- **FLOATING LABEL INPUTS (preferred for text/password/email inputs):**
  - floating-input-wrapper: wrapper div for floating label inputs
  - floating-input: the input element (use instead of input-field)
  - floating-label: the label element (use instead of form-label)
  - Structure: <div class="input-container"><div class="floating-input-wrapper"><input class="floating-input" name="" placeholder="" type=""><label class="floating-label"></div></div>
- **LEGACY INPUTS (only for special cases):**
  - form-label, input-field: traditional label above input
- select-container, select-field
- checkbox-container, checkbox-field, checkbox-label
- radio-container, radio-option, radio-field
- button-container, button-primary, button-secondary
- form-error, otp-container, otp-input
- form-section, form-section-title

### Input Field Guidelines:
- **USE traditional labels for:** checkboxes, radio buttons, select dropdowns
- **ALWAYS set placeholder="" (empty) for floating inputs**
- **ALWAYS include proper for/id attributes for accessibility**

### Example Floating Label Structure:
For username input: input-container > floating-input-wrapper > (floating-input + floating-label)
For password input: input-container > floating-input-wrapper > (floating-input + floating-label)
Remember: floating-input must have placeholder="" and floating-label must come after the input

Response example:
Required JSON format:
{
  "htmx": "<form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML"><div class="input-container"><div class="floating-input-wrapper"> <input class="floating-input" name="email" placeholder="" type="email"> <label class="floating-label">Enter your email</label></div></div><div class="input-container"><div class="floating-input-wrapper"> <input class="floating-input" name="password" placeholder="" type="password"> <label class="floating-label">Enter your password</label></div></div><div class="form-error">Wrong password. Try again or click Forgot password to reset it.</div><div class="button-container"><button class="button-primary" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click">Next</button></div></form>"
}
  `,
  complex: `
${monolithicFormPromptInstructions}
  `,
};

interface TestResult {
  duration: number;
  promptType: string;
  outputTokens: number;
  outputText: string;
  timestamp: number;
}

interface TestStatistics {
  average: number;
  min: number;
  max: number;
  median: number;
  stdDev: number;
  total: number;
  count: number;
}

function calculateStatistics(values: number[]): TestStatistics {
  if (values.length === 0) {
    throw new Error('Cannot calculate statistics on empty array');
  }

  const sorted = [...values].sort((a, b) => a - b);

  const total = values.reduce((sum, val) => sum + val, 0);
  const average = total / values.length;

  const mid = Math.floor(sorted.length / 2);
  const median = sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];

  const squareDiffs = values.map((value) => {
    const diff = value - average;
    return diff * diff;
  });
  const avgSquareDiff = squareDiffs.reduce((sum, val) => sum + val, 0) / values.length;
  const stdDev = Math.sqrt(avgSquareDiff);

  return {
    average,
    min: sorted[0],
    max: sorted[sorted.length - 1],
    median,
    stdDev,
    total,
    count: values.length,
  };
}

function formatStatsRow(label: string, stats: TestStatistics): string {
  return `| ${label} | ${stats.average.toFixed(2)} | ${stats.min.toFixed(2)} | ${stats.max.toFixed(2)} | ${stats.median.toFixed(2)} | ${stats.stdDev.toFixed(2)} |`;
}

function countTokens(text: string): number {
  return encode(text).length;
}

async function runGeminiTest(
  imageData: string,
  prompt: string,
  maxTokens: number = 150,
): Promise<TestResult> {
  const startTime = Date.now();

  try {
    const imageData = await imageToBase64(
      `${__dirname.replace('/scripts', '/files/screenshots/google_login.webp')}`,
    );

    const geminiRepository = new GeminiLLMRepository(
      GEMINI_API_KEY,
      'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio',
    );

    const response = await geminiRepository.getLLMResponse({
      platform: 'facebook',
      prompt: prompt,
      screenshot: imageData,
      skipCache: true,
      viewportWidth: 1080,
      viewportHeight: 720,
      version: 'v1',
      responseSchema: EXTRACTION_RESPONSE_SCHEMA,
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    const outputText = response.output_text;

    return {
      duration,
      promptType:
        prompt === TEST_PROMPTS.simple
          ? 'simple'
          : prompt === TEST_PROMPTS.medium
            ? 'medium'
            : 'complex',
      outputTokens: countTokens(outputText),
      outputText,
      timestamp: endTime,
    };
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
}

export async function testPerformance(iterations: number = DEFAULT_ITERATIONS) {
  if (!GEMINI_API_KEY) {
    console.log('Please insert your Gemini API key in the script');
    return;
  }

  console.log('Loading and preparing test image...');
  const imageData = await imageToBase64(
    `${__dirname.replace('/scripts', '/files/screenshots/google_login.webp')}`,
  );
  console.log(`Image prepared successfully`);

  const results: Record<string, TestResult[]> = {
    simple: [],
    medium: [],
    complex: [],
  };

  console.log(`\n# Testing Gemini Model Performance`);
  console.log(`Running ${iterations} iterations for each prompt complexity level...\n`);

  for (const [promptType, prompt] of Object.entries(TEST_PROMPTS)) {
    console.log(`\n## Testing ${promptType} prompts`);

    for (let i = 0; i < iterations; i++) {
      process.stdout.write(`Running iteration ${i + 1}/${iterations}... `);

      try {
        const result = await runGeminiTest(imageData, prompt);
        results[promptType].push(result);
        process.stdout.write(`Done (${result.duration}ms)\n`);
      } catch (error) {
        process.stdout.write(`Failed\n`);
        console.error(`Error in ${promptType} prompt, iteration ${i + 1}:`, error);
      }
    }
  }

  // Calculate statistics
  const durationStats: Record<string, TestStatistics> = {};
  const tokenStats: Record<string, TestStatistics> = {};

  for (const promptType of Object.keys(results)) {
    if (results[promptType].length > 0) {
      durationStats[promptType] = calculateStatistics(results[promptType].map((r) => r.duration));
      tokenStats[promptType] = calculateStatistics(results[promptType].map((r) => r.outputTokens));
    }
  }

  console.log('\n# Gemini Model Performance\n');

  console.log('## Response Time Comparison (ms)\n');
  console.log('| Prompt Type | Avg (ms) | Min (ms) | Max (ms) | Median (ms) | Std Dev |');
  console.log('|-------------|----------|----------|----------|-------------|---------|');

  for (const promptType of Object.keys(durationStats)) {
    console.log(formatStatsRow(promptType, durationStats[promptType]));
  }

  console.log('\n## Output Token Comparison\n');
  console.log('| Prompt Type | Avg Tokens | Min Tokens | Max Tokens | Median Tokens | Std Dev |');
  console.log('|-------------|------------|------------|------------|---------------|---------|');

  for (const promptType of Object.keys(tokenStats)) {
    console.log(formatStatsRow(promptType, tokenStats[promptType]));
  }

  console.log('\n## Token Generation Speed (tokens/second)\n');
  console.log('| Prompt Type | Tokens/Second |');
  console.log('|-------------|---------------|');

  for (const promptType of Object.keys(durationStats)) {
    const tokensPerSecond =
      tokenStats[promptType].average / (durationStats[promptType].average / 1000);
    console.log(`| ${promptType} | ${tokensPerSecond.toFixed(2)} |`);
  }

  // Sample outputs
  console.log('\n## Sample Outputs\n');

  for (const promptType of Object.keys(results)) {
    if (results[promptType].length > 0) {
      console.log(`### ${promptType} prompt\n`);
      console.log('```');
      // @ts-ignore
      console.log(`Prompt: ${TEST_PROMPTS[promptType]}`);
      console.log('```\n');
      console.log('```');
      console.log(results[promptType][0].outputText);
      console.log('```\n');
    }
  }
}

async function runSingleCall(prompt: string): Promise<LLMResponse> {
  if (!GEMINI_API_KEY) {
    console.log('Please insert your Gemini API key in the script');
    throw new Error('Please insert your Gemini API key in the script');
  }
  console.log('Loading...');
  const startTime = Date.now();

  try {
    const imageData = await imageToBase64(`${__dirname.replace('/scripts', workingFile)}`);

    const geminiRepository = new GeminiLLMRepository(
      GEMINI_API_KEY,
      'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio',
    );

    const response = await geminiRepository.getLLMResponse({
      platform: 'facebook',
      prompt: prompt,
      screenshot: imageData,
      skipCache: true,
      viewportWidth: 1080,
      viewportHeight: 720,
      version: 'v1',
      responseSchema: EXTRACTION_RESPONSE_SCHEMA,
    });
    console.log(response.output_text);

    const endTime = Date.now();
    const duration = endTime - startTime;
    console.log(`Duration: ${duration}ms`);

    return response;
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
}

const action = process.argv[2];
const iterations = process.argv[3] ? parseInt(process.argv[3], 10) : DEFAULT_ITERATIONS;

export const FORM_VISION_PROMPT_V6 = `
Task: From the image, extract a JSON object of the UI controls essential for an existing user's authentication flow.

**Extraction Rules**

1.  **Permitted Controls:**
    *   Extract controls for standard credential entry (e.g., username/email fields, password fields).
    *   Extract controls for Multi-Factor Authentication (MFA) or identity verification (e.g., radio buttons for choosing a method, fields for entering a code).
    *   Extract controls that allow the user to remain authenticated or have their device remembered (e.g., "Remember me," "Stay signed in," "Trust this device").
    *   Extract the primary submission button required to proceed (e.g., "Sign In," "Continue," "Verify").

2.  **Radio Group Handling:**
    *   When you identify a group of mutually exclusive radio buttons, you **must** consolidate them into a **single** \`field\` object with \`fieldControlType: 'radiogroup'\`.
    *   The \`label\` for this single field object must be the main question or heading for the entire group (e.g., "Choose a way to confirm it's you").
    *   The \`options\` array of this single field object must contain an entry for each individual radio button. Each entry should include its own \`id\`, \`label\`, \`description\` (if any sub-text exists), and \`checked\` status.
    *   **Do not** create a separate \`field\` object for each individual radio button.

2.  **Explicit Exclusions:**
    *   Strictly ignore all controls related to **Account Creation**, **Account Recovery**, **Show/Hide Passwords** and **Profile Management**.

3.  **Grounding:**
    *   Only extract controls that are visibly present in the image. Do not infer or hallucinate.
`;

export const FORM_VISION_CLASSIFICATION_PROMPT_V34 = `
Task: Your function is to analyze the image to determine its primary classification and the user's authentication status. Apply the rules below in order, selecting the first one that matches. Only output a raw JSON object without any fencing or additional text.

**Prioritized Classification & AuthState Rules**

1.  **"loading-screen":**
    * **Classification:** The primary visible content indicates a process is in progress, including (but not limited to):
        - Spinners, rotating circles, pulsing dots, animated loaders.
        - Progress bars (determinate or indeterminate).
        - “Loading…”, “Please wait”, “Fetching data” text.
        - Blurred, greyed-out, or skeleton placeholders (UI shapes without content).
        - Splash/loading pages with minimal branding and no interactive fields.
    *   **\`authState\` Rule:** Set to \`not-authenticated\`. A loading screen during login is the most common case.

2.  **"captcha-screen":**
    *   **Classification:** The screen contains a challenge to distinguish a human from a bot (e.g., reCAPTCHA, hCaptcha, image puzzles).
    *   **\`authState\` Rule:** Set to \`not-authenticated\`. CAPTCHAs are most frequently part of a login or signup flow.

3.  **"passkey-screen":**
    *   **Classification:** The screen explicitly prompts for a passkey, security key, WebAuthn, FIDO, or a biometric identifier.
    *   **\`authState\` Rule:
          ** If the screen's indicates the user is being prompted to **create** a passkey. Set to \`authenticated\`. Set \`authStateReasoning\` to "User is authenticated"
          ** If the screen's indicates the user is being prompted to **use** a passkey, Set to \`not-authenticated\`. Set \`authStateReasoning\` to "User is not authenticated."
    *   **\`title\` Rule:** Override value and set to \`Passkey Authentication\`.
    *   **\`description\` Rule:** Override value and set to \`Sorry, Passkeys are not supported\`.

4.  **"multi-factor-verification-screen":**
    *   **multi-factor-push-approval-screen:**
          **Classification:** The screen is part of a multi-factor authentication (MFA) flow, asking the user to enter a verification code or approve the login from another device. At this stage of the login flow, the user is not yet authenticated.
          **\`authState\` Rule:** Set to \`not-authenticated\`.
          **Extraction Rule:** Look for a visible one-time code presented as static text on the screen (e.g., "Your code is 123-456"). If a code is found, extract the numeric or alphanumeric string and place it in the \`verificationCode\` field. If the MFA method does not display a code (e.g., it's a push notification) or no code is visible, the \`verificationCode\` field should be set to null.

    *   **multi-factor-code-verification-screen:**
          **Classification:** The screen is part of a multi-factor authentication (MFA) flow, asking the user to enter a verification code. At this stage of the login flow, the user is not yet authenticated.
          **\`authState\` Rule:** Set to \`not-authenticated\`.

    *   **multi-factor-multiple-options-screen:**
          **Classification:** The screen is part of a multi-factor authentication (MFA) flow, asking the user to select from multiple verification options (e.g., "Send code via SMS", "Use authenticator app", "Email me a code"). At this stage of the login flow, the user is not yet authenticated AND the user has not selected a verification option.
          **\`authState\` Rule:** Set to \`not-authenticated\`.

5.  **"trust-device-screen":**
    *   **Classification:** The screen asks the user if they want to "trust" or "remember" the current device to simplify future logins. Look for phrases like "Trust this browser?", "Remember me", or "Stay signed in?".
    *   **\`authState\` Rule:** The user has proven their identity but the login flow is not complete. Set to \`authenticated\`.

6.  **"profile-management-screen":**
    *   **Classification:** The screen allows a user to view or change their account settings (e.g., updating personal info, changing a password).
    *   **\`authState\` Rule:** A user must be logged in to manage their profile. This is the primary indicator of an active session. Set to \`authenticated\`.

7. **"logged-in-screen":**
    *   **Classification:** The screen shows a post-authentication experience (e.g., dashboard, home/feed, inbox, account/usage overview, order history) and includes at least one signed-in indicator such as “Welcome back, [name]”, a visible user avatar/profile menu, a “Sign out/Log out” action, or a user identifier (email/username) shown as part of the UI—not inside a login form. It is not an MFA, passkey, trust-device, loading, captcha, or dedicated profile/settings editor screen.
    *   **\`authState\` Rule:** Set to \`authenticated\`.
    *   Notes: If any earlier rule (1–6) matches, use that earlier rule instead of this one.

8.  **"other":**
    *   **Classification:** The screen does not match any of the above categories. This includes standard username/password login forms, account creation pages, and generic error screens.
    *   **\`authState\` Rule:** These screens all precede a successful login. Set to \`not-authenticated\`.

**Grounding:**
*   Base your classification and \`authState\` decision solely on the visible content of the image and the rules above.

Version: 10
`;

const workingFile = '/files/screenshots/microsoft-keep-me-signed-in.png';
console.log(workingFile);
switch (action) {
  case 'single':
    runSingleCall(FORM_VISION_PROMPT_V6);
    break;
  case 'performance':
    testPerformance(iterations);
    break;
}
