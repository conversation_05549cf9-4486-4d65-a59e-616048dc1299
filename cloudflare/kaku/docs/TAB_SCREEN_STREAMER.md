# Tab Screen Streamer

A browser-injectable script that captures and streams the current tab's screen content to the UI layout component. This implementation follows the established patterns from the existing screen-cropper architecture while being specifically designed for tab-level screen streaming.

## Overview

The Tab Screen Streamer provides a lightweight alternative to the full screen-cropper implementation, focusing specifically on capturing and streaming the current browser tab's visual content. It uses the same WebRTC and WebSocket infrastructure as the screen-cropper but with a simplified interface optimized for tab streaming.

## Architecture

### Core Components

1. **WebSocket Connection**: Establishes communication channel with the UI layout
2. **WebRTC Peer Connection**: Handles real-time video streaming
3. **Display Media API**: Captures the current tab's screen content
4. **Input Data Channel**: Enables bidirectional communication for control messages

### Integration Points

- **UI Layout Component**: Receives and displays the streamed content
- **Client API Abstraction**: Provides typed interface for CDP operations
- **Cross-Tab Communication**: Compatible with the two-tab architecture

## Usage

### Basic Initialization

```javascript
// Initialize the tab screen streamer
await window.tabScreenStreamer.init(wsEndpoint, viewport);

// Start streaming
await window.tabScreenStreamer.start(viewport);

// Stop streaming
await window.tabScreenStreamer.stop();
```

### Using the Client API Abstraction

```typescript
import { withCdp } from '../browser/client-api';

const clientScripts = withCdp(cdpClient, executionContextId, sessionId);

// Initialize tab screen streamer
await clientScripts.TabScreenStreamer.init(wsEndpoint, viewport);

// Start streaming
await clientScripts.TabScreenStreamer.start(viewport);

// Check status
const status = await clientScripts.TabScreenStreamer.getStatus();

// Cleanup
await clientScripts.TabScreenStreamer.cleanup();
```

## API Reference

### Methods

#### `init(wsEndpoint: string, viewport: Viewport): Promise<'SUCCESS' | 'ALREADY_INITIALIZED'>`

Initializes the tab screen streamer with WebSocket and WebRTC connections.

**Parameters:**
- `wsEndpoint`: WebSocket endpoint URL for communication
- `viewport`: Viewport dimensions `{ width: number, height: number }`

**Returns:** Promise resolving to initialization status

#### `start(viewport: Viewport): Promise<'SUCCESS'>`

Starts streaming the current tab's screen content.

**Parameters:**
- `viewport`: Viewport dimensions for the stream

**Returns:** Promise resolving to success status

#### `stop(): Promise<'SUCCESS'>`

Stops the streaming and cleans up the media stream.

**Returns:** Promise resolving to success status

#### `getStatus(): TabScreenStreamerStatus`

Returns the current status of the tab screen streamer.

**Returns:** Status object containing:
- `initialized`: Whether the streamer is initialized
- `streaming`: Whether streaming is active
- `socketReady`: WebSocket connection status
- `webrtcState`: WebRTC peer connection state
- `iceState`: ICE connection state

#### `cleanup(): void`

Cleans up all resources, connections, and event listeners.

## Message Protocol

### Outgoing Messages (Tab → UI)

#### Tab Streaming Status
```json
{
  "type": "tab-streaming-status",
  "status": "started" | "stopped" | "ended" | "error",
  "timestamp": 1234567890,
  "viewport": { "width": 1366, "height": 768 },
  "reason": "track_ended",
  "error": "Error message"
}
```

### Incoming Messages (UI → Tab)

#### Start Streaming
```json
{
  "type": "start-streaming",
  "viewport": { "width": 1366, "height": 768 }
}
```

#### Stop Streaming
```json
{
  "type": "stop-streaming"
}
```

## Integration with Existing Architecture

### Screen Cropper Compatibility

The Tab Screen Streamer is designed to work alongside the existing screen-cropper implementation:

- **Shared Infrastructure**: Uses the same WebSocket and WebRTC patterns
- **Compatible Message Protocol**: Follows established message formats
- **UI Integration**: Integrates with the same layout.ts component
- **Type Safety**: Includes full TypeScript definitions

### Two-Tab Architecture Support

The Tab Screen Streamer is compatible with the two-tab architecture:

- **Target Tab Usage**: Designed for injection into target tabs
- **Cross-Tab Communication**: Can work with the cross-tab communicator
- **CDP Integration**: Includes client API abstraction for CDP operations

## Error Handling

The Tab Screen Streamer includes comprehensive error handling:

- **Connection Errors**: WebSocket and WebRTC connection failures
- **Stream Errors**: Media stream acquisition and processing errors
- **Track Ended Events**: Automatic cleanup when streams end
- **Status Updates**: Real-time status reporting to the UI

## Performance Considerations

- **Lightweight Implementation**: Minimal overhead compared to full screen-cropper
- **Efficient Streaming**: Uses WebRTC for optimal performance
- **Resource Cleanup**: Automatic cleanup on page unload
- **Frame Rate Control**: Configurable frame rate (default: 15fps)

## Browser Compatibility

The Tab Screen Streamer requires:

- **Display Media API**: For screen capture (`getDisplayMedia`)
- **WebRTC Support**: For peer-to-peer streaming
- **WebSocket Support**: For signaling and control
- **Modern Browser**: Chrome 88+, Firefox 94+, Safari 15.4+

## Security Considerations

- **User Permission**: Requires user permission for screen sharing
- **Same-Origin Policy**: Respects browser security policies
- **Secure Connections**: Uses secure WebSocket (WSS) and TURN servers
- **Resource Isolation**: Proper cleanup prevents resource leaks
