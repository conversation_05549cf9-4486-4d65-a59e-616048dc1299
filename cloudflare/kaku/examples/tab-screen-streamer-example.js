/**
 * Tab Screen Streamer Usage Example
 *
 * This example demonstrates how to use the tab screen streamer
 * to capture and stream the current tab's screen content.
 */

// Example WebSocket endpoint (replace with actual endpoint)
const WS_ENDPOINT = 'ws://localhost:9222/devtools/browser/d6316a7f-b65e-4612-9cf8-22dc46deedee';

// Example viewport configuration
const VIEWPORT = {
  width: 1366,
  height: 768,
};

/**
 * Basic usage example
 */
async function basicUsageExample() {
  try {
    console.log('🚀 Starting tab screen streamer example...');

    // Check if tab screen streamer is available
    if (!window.tabScreenStreamer) {
      throw new Error('Tab screen streamer not available. Make sure the script is injected.');
    }

    // Initialize the tab screen streamer
    console.log('📡 Initializing tab screen streamer...');
    const initResult = await window.tabScreenStreamer.init(WS_ENDPOINT, VIEWPORT);
    console.log('✅ Initialization result:', initResult);

    // Check status after initialization
    const statusAfterInit = window.tabScreenStreamer.getStatus();
    console.log('📊 Status after init:', statusAfterInit);

    // Start streaming
    console.log('🎬 Starting tab screen streaming...');
    const startResult = await window.tabScreenStreamer.start(VIEWPORT);
    console.log('✅ Start result:', startResult);

    // Check status after starting
    const statusAfterStart = window.tabScreenStreamer.getStatus();
    console.log('📊 Status after start:', statusAfterStart);

    // Stream for 10 seconds
    console.log('⏱️ Streaming for 10 seconds...');
    await new Promise((resolve) => setTimeout(resolve, 10000));

    // Stop streaming
    console.log('🛑 Stopping tab screen streaming...');
    const stopResult = await window.tabScreenStreamer.stop();
    console.log('✅ Stop result:', stopResult);

    // Check final status
    const finalStatus = window.tabScreenStreamer.getStatus();
    console.log('📊 Final status:', finalStatus);

    console.log('🎉 Tab screen streamer example completed successfully!');
  } catch (error) {
    console.error('❌ Tab screen streamer example failed:', error);
  }
}

/**
 * Advanced usage example with error handling and status monitoring
 */
async function advancedUsageExample() {
  try {
    console.log('🚀 Starting advanced tab screen streamer example...');

    // Status monitoring function
    const monitorStatus = () => {
      const status = window.tabScreenStreamer.getStatus();
      console.log('📊 Current status:', {
        initialized: status.initialized,
        streaming: status.streaming,
        socketReady: status.socketReady,
        webrtcState: status.webrtcState,
        iceState: status.iceState,
      });
    };

    // Initialize with error handling
    try {
      await window.tabScreenStreamer.init(WS_ENDPOINT, VIEWPORT);
      console.log('✅ Initialization successful');
    } catch (error) {
      console.error('❌ Initialization failed:', error);
      return;
    }

    // Monitor status every 2 seconds
    const statusInterval = setInterval(monitorStatus, 2000);

    // Start streaming with retry logic
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        await window.tabScreenStreamer.start(VIEWPORT);
        console.log('✅ Streaming started successfully');
        break;
      } catch (error) {
        retryCount++;
        console.warn(`⚠️ Start attempt ${retryCount} failed:`, error);

        if (retryCount < maxRetries) {
          console.log(`🔄 Retrying in 2 seconds... (${retryCount}/${maxRetries})`);
          await new Promise((resolve) => setTimeout(resolve, 2000));
        } else {
          console.error('❌ All start attempts failed');
          clearInterval(statusInterval);
          return;
        }
      }
    }

    // Stream for 15 seconds
    console.log('⏱️ Streaming for 15 seconds...');
    await new Promise((resolve) => setTimeout(resolve, 15000));

    // Stop monitoring
    clearInterval(statusInterval);

    // Stop streaming
    try {
      await window.tabScreenStreamer.stop();
      console.log('✅ Streaming stopped successfully');
    } catch (error) {
      console.error('❌ Stop failed:', error);
    }

    // Final cleanup
    try {
      window.tabScreenStreamer.cleanup();
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('❌ Cleanup failed:', error);
    }

    console.log('🎉 Advanced tab screen streamer example completed!');
  } catch (error) {
    console.error('❌ Advanced example failed:', error);
  }
}

/**
 * Integration example with existing screen cropper patterns
 */
async function integrationExample() {
  try {
    console.log('🚀 Starting integration example...');

    // Check for both screen cropper and tab screen streamer
    const hasScreenCropper = !!window.screenCropper;
    const hasTabStreamer = !!window.tabScreenStreamer;

    console.log('🔍 Available components:', {
      screenCropper: hasScreenCropper,
      tabScreenStreamer: hasTabStreamer,
    });

    if (!hasTabStreamer) {
      throw new Error('Tab screen streamer not available');
    }

    // Initialize tab screen streamer
    await window.tabScreenStreamer.init(WS_ENDPOINT, VIEWPORT);

    // Example: Use tab streamer as fallback if screen cropper fails
    let streamingMethod = 'none';

    if (hasScreenCropper) {
      try {
        // Try screen cropper first
        await window.screenCropper.init(WS_ENDPOINT, VIEWPORT);
        await window.screenCropper.start(VIEWPORT);
        streamingMethod = 'screen-cropper';
        console.log('✅ Using screen cropper for streaming');
      } catch (error) {
        console.warn('⚠️ Screen cropper failed, falling back to tab streamer:', error);

        // Fallback to tab screen streamer
        await window.tabScreenStreamer.start(VIEWPORT);
        streamingMethod = 'tab-streamer';
        console.log('✅ Using tab screen streamer as fallback');
      }
    } else {
      // Use tab screen streamer directly
      await window.tabScreenStreamer.start(VIEWPORT);
      streamingMethod = 'tab-streamer';
      console.log('✅ Using tab screen streamer directly');
    }

    // Stream for 8 seconds
    console.log(`⏱️ Streaming with ${streamingMethod} for 8 seconds...`);
    await new Promise((resolve) => setTimeout(resolve, 8000));

    // Stop the appropriate streamer
    if (streamingMethod === 'screen-cropper') {
      window.screenCropper.stopStreaming();
    } else if (streamingMethod === 'tab-streamer') {
      await window.tabScreenStreamer.stop();
    }

    console.log('🎉 Integration example completed!');
  } catch (error) {
    console.error('❌ Integration example failed:', error);
  }
}

// Export examples for use
window.tabScreenStreamerExamples = {
  basic: basicUsageExample,
  advanced: advancedUsageExample,
  integration: integrationExample,
};

console.log('📚 Tab Screen Streamer examples loaded. Available methods:');
console.log('- window.tabScreenStreamerExamples.basic()');
console.log('- window.tabScreenStreamerExamples.advanced()');
console.log('- window.tabScreenStreamerExamples.integration()');
